<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentStateMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="version_id" jdbcType="VARCHAR" property="versionId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState">
    <result column="team_state" jdbcType="LONGVARCHAR" property="teamState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, session_id, version_id, add_time, status
  </sql>
  <sql id="Blob_Column_List">
    team_state
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentStateExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_states
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentStateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from agent_states
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_states
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from agent_states
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentStateExample">
    delete from agent_states
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState">
    insert into agent_states (id, session_id, version_id, 
      add_time, status, team_state
      )
    values (#{id,jdbcType=BIGINT}, #{sessionId,jdbcType=VARCHAR}, #{versionId,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{teamState,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState">
    insert into agent_states
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="versionId != null">
        version_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="teamState != null">
        team_state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null">
        #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="teamState != null">
        #{teamState,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentStateExample" resultType="java.lang.Long">
    select count(*) from agent_states
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update agent_states
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.versionId != null">
        version_id = #{row.versionId,jdbcType=VARCHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.teamState != null">
        team_state = #{row.teamState,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update agent_states
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      version_id = #{row.versionId,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      status = #{row.status,jdbcType=INTEGER},
      team_state = #{row.teamState,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update agent_states
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      version_id = #{row.versionId,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      status = #{row.status,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState">
    update agent_states
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null">
        version_id = #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="teamState != null">
        team_state = #{teamState,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState">
    update agent_states
    set session_id = #{sessionId,jdbcType=VARCHAR},
      version_id = #{versionId,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      team_state = #{teamState,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState">
    update agent_states
    set session_id = #{sessionId,jdbcType=VARCHAR},
      version_id = #{versionId,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>