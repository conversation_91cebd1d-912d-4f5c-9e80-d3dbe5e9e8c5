<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentSessionMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession">
    <id column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="agent_code" jdbcType="VARCHAR" property="agentCode" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession">
    <result column="show_message" jdbcType="LONGVARCHAR" property="showMessage" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    session_id, owner, agent_code, title, updated_at, status
  </sql>
  <sql id="Blob_Column_List">
    show_message
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentSessionExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_sessions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentSessionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from agent_sessions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_sessions
    where session_id = #{sessionId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from agent_sessions
    where session_id = #{sessionId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentSessionExample">
    delete from agent_sessions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession">
    insert into agent_sessions (session_id, owner, agent_code, 
      title, updated_at, status, 
      show_message)
    values (#{sessionId,jdbcType=VARCHAR}, #{owner,jdbcType=VARCHAR}, #{agentCode,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, 
      #{showMessage,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession">
    insert into agent_sessions
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="agentCode != null">
        agent_code,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="showMessage != null">
        show_message,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="agentCode != null">
        #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="showMessage != null">
        #{showMessage,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentSessionExample" resultType="java.lang.Long">
    select count(*) from agent_sessions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update agent_sessions
    <set>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.owner != null">
        owner = #{row.owner,jdbcType=VARCHAR},
      </if>
      <if test="row.agentCode != null">
        agent_code = #{row.agentCode,jdbcType=VARCHAR},
      </if>
      <if test="row.title != null">
        title = #{row.title,jdbcType=VARCHAR},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.showMessage != null">
        show_message = #{row.showMessage,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update agent_sessions
    set session_id = #{row.sessionId,jdbcType=VARCHAR},
      owner = #{row.owner,jdbcType=VARCHAR},
      agent_code = #{row.agentCode,jdbcType=VARCHAR},
      title = #{row.title,jdbcType=VARCHAR},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      status = #{row.status,jdbcType=INTEGER},
      show_message = #{row.showMessage,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update agent_sessions
    set session_id = #{row.sessionId,jdbcType=VARCHAR},
      owner = #{row.owner,jdbcType=VARCHAR},
      agent_code = #{row.agentCode,jdbcType=VARCHAR},
      title = #{row.title,jdbcType=VARCHAR},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      status = #{row.status,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession">
    update agent_sessions
    <set>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="agentCode != null">
        agent_code = #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="showMessage != null">
        show_message = #{showMessage,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where session_id = #{sessionId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession">
    update agent_sessions
    set owner = #{owner,jdbcType=VARCHAR},
      agent_code = #{agentCode,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      show_message = #{showMessage,jdbcType=LONGVARCHAR}
    where session_id = #{sessionId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession">
    update agent_sessions
    set owner = #{owner,jdbcType=VARCHAR},
      agent_code = #{agentCode,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER}
    where session_id = #{sessionId,jdbcType=VARCHAR}
  </update>
</mapper>