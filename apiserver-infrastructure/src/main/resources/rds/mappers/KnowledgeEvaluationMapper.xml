<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.KnowledgeEvaluationMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="partition_date" jdbcType="VARCHAR" property="partitionDate" />
    <result column="agent_code" jdbcType="VARCHAR" property="agentCode" />
    <result column="knowledge_key" jdbcType="VARCHAR" property="knowledgeKey" />
    <result column="collect_id" jdbcType="BIGINT" property="collectId" />
    <result column="knowledge_data" jdbcType="CHAR" property="knowledgeData" />
    <result column="eval_status" jdbcType="INTEGER" property="evalStatus" />
    <result column="eval_result" jdbcType="INTEGER" property="evalResult" />
    <result column="eval_report" jdbcType="CHAR" property="evalReport" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, partition_date, agent_code, knowledge_key, collect_id, knowledge_data, eval_status,
    eval_result, eval_report, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeEvaluationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from agent_knowledge_evaluation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from agent_knowledge_evaluation
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from agent_knowledge_evaluation
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeEvaluationExample">
    delete from agent_knowledge_evaluation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation">
    insert into agent_knowledge_evaluation (id, partition_date, agent_code,
      knowledge_key, collect_id, knowledge_data,
      eval_status, eval_result, eval_report,
      add_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{partitionDate,jdbcType=VARCHAR}, #{agentCode,jdbcType=VARCHAR},
      #{knowledgeKey,jdbcType=VARCHAR}, #{collectId,jdbcType=BIGINT}, #{knowledgeData,jdbcType=CHAR},
      #{evalStatus,jdbcType=INTEGER}, #{evalResult,jdbcType=INTEGER}, #{evalReport,jdbcType=CHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation">
    insert into agent_knowledge_evaluation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="partitionDate != null">
        partition_date,
      </if>
      <if test="agentCode != null">
        agent_code,
      </if>
      <if test="knowledgeKey != null">
        knowledge_key,
      </if>
      <if test="collectId != null">
        collect_id,
      </if>
      <if test="knowledgeData != null">
        knowledge_data,
      </if>
      <if test="evalStatus != null">
        eval_status,
      </if>
      <if test="evalResult != null">
        eval_result,
      </if>
      <if test="evalReport != null">
        eval_report,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partitionDate != null">
        #{partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="agentCode != null">
        #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="knowledgeKey != null">
        #{knowledgeKey,jdbcType=VARCHAR},
      </if>
      <if test="collectId != null">
        #{collectId,jdbcType=BIGINT},
      </if>
      <if test="knowledgeData != null">
        #{knowledgeData,jdbcType=CHAR},
      </if>
      <if test="evalStatus != null">
        #{evalStatus,jdbcType=INTEGER},
      </if>
      <if test="evalResult != null">
        #{evalResult,jdbcType=INTEGER},
      </if>
      <if test="evalReport != null">
        #{evalReport,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeEvaluationExample" resultType="java.lang.Long">
    select count(*) from agent_knowledge_evaluation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update agent_knowledge_evaluation
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.partitionDate != null">
        partition_date = #{row.partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="row.agentCode != null">
        agent_code = #{row.agentCode,jdbcType=VARCHAR},
      </if>
      <if test="row.knowledgeKey != null">
        knowledge_key = #{row.knowledgeKey,jdbcType=VARCHAR},
      </if>
      <if test="row.collectId != null">
        collect_id = #{row.collectId,jdbcType=BIGINT},
      </if>
      <if test="row.knowledgeData != null">
        knowledge_data = #{row.knowledgeData,jdbcType=CHAR},
      </if>
      <if test="row.evalStatus != null">
        eval_status = #{row.evalStatus,jdbcType=INTEGER},
      </if>
      <if test="row.evalResult != null">
        eval_result = #{row.evalResult,jdbcType=INTEGER},
      </if>
      <if test="row.evalReport != null">
        eval_report = #{row.evalReport,jdbcType=CHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update agent_knowledge_evaluation
    set id = #{row.id,jdbcType=BIGINT},
      partition_date = #{row.partitionDate,jdbcType=VARCHAR},
      agent_code = #{row.agentCode,jdbcType=VARCHAR},
      knowledge_key = #{row.knowledgeKey,jdbcType=VARCHAR},
      collect_id = #{row.collectId,jdbcType=BIGINT},
      knowledge_data = #{row.knowledgeData,jdbcType=CHAR},
      eval_status = #{row.evalStatus,jdbcType=INTEGER},
      eval_result = #{row.evalResult,jdbcType=INTEGER},
      eval_report = #{row.evalReport,jdbcType=CHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation">
    update agent_knowledge_evaluation
    <set>
      <if test="agentCode != null">
        agent_code = #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="knowledgeKey != null">
        knowledge_key = #{knowledgeKey,jdbcType=VARCHAR},
      </if>
      <if test="collectId != null">
        collect_id = #{collectId,jdbcType=BIGINT},
      </if>
      <if test="knowledgeData != null">
        knowledge_data = #{knowledgeData,jdbcType=CHAR},
      </if>
      <if test="evalStatus != null">
        eval_status = #{evalStatus,jdbcType=INTEGER},
      </if>
      <if test="evalResult != null">
        eval_result = #{evalResult,jdbcType=INTEGER},
      </if>
      <if test="evalReport != null">
        eval_report = #{evalReport,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation">
    update agent_knowledge_evaluation
    set agent_code = #{agentCode,jdbcType=VARCHAR},
      knowledge_key = #{knowledgeKey,jdbcType=VARCHAR},
      collect_id = #{collectId,jdbcType=BIGINT},
      knowledge_data = #{knowledgeData,jdbcType=CHAR},
      eval_status = #{evalStatus,jdbcType=INTEGER},
      eval_result = #{evalResult,jdbcType=INTEGER},
      eval_report = #{evalReport,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </update>

  <!-- 根据evalStatus查询记录（带limit限制） -->
  <select id="selectByEvalStatusWithLimit" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from agent_knowledge_evaluation
    where eval_status = #{evalStatus,jdbcType=INTEGER}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
    limit #{limit,jdbcType=INTEGER}
  </select>

  <!-- 批量更新记录的evalStatus -->
  <update id="batchUpdateEvalStatus">
    update agent_knowledge_evaluation
    set eval_status = #{newStatus,jdbcType=INTEGER},
        update_time = NOW()
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>