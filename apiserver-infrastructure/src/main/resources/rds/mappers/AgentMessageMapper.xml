<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentMessageMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="message_type" jdbcType="VARCHAR" property="messageType" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="attachment" jdbcType="VARCHAR" property="attachment" />
    <result column="attachment_type" jdbcType="VARCHAR" property="attachmentType" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, msg_id, session_id, message_type, source, attachment, attachment_type, add_time
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentMessageExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_messages
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentMessageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from agent_messages
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_messages
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from agent_messages
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentMessageExample">
    delete from agent_messages
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage">
    insert into agent_messages (id, msg_id, session_id, 
      message_type, source, attachment, 
      attachment_type, add_time, content
      )
    values (#{id,jdbcType=BIGINT}, #{msgId,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, 
      #{messageType,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{attachment,jdbcType=VARCHAR}, 
      #{attachmentType,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage">
    insert into agent_messages
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="messageType != null">
        message_type,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="attachment != null">
        attachment,
      </if>
      <if test="attachmentType != null">
        attachment_type,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null">
        #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="attachment != null">
        #{attachment,jdbcType=VARCHAR},
      </if>
      <if test="attachmentType != null">
        #{attachmentType,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentMessageExample" resultType="java.lang.Long">
    select count(*) from agent_messages
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update agent_messages
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.msgId != null">
        msg_id = #{row.msgId,jdbcType=VARCHAR},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.messageType != null">
        message_type = #{row.messageType,jdbcType=VARCHAR},
      </if>
      <if test="row.source != null">
        source = #{row.source,jdbcType=VARCHAR},
      </if>
      <if test="row.attachment != null">
        attachment = #{row.attachment,jdbcType=VARCHAR},
      </if>
      <if test="row.attachmentType != null">
        attachment_type = #{row.attachmentType,jdbcType=VARCHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update agent_messages
    set id = #{row.id,jdbcType=BIGINT},
      msg_id = #{row.msgId,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      message_type = #{row.messageType,jdbcType=VARCHAR},
      source = #{row.source,jdbcType=VARCHAR},
      attachment = #{row.attachment,jdbcType=VARCHAR},
      attachment_type = #{row.attachmentType,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      content = #{row.content,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update agent_messages
    set id = #{row.id,jdbcType=BIGINT},
      msg_id = #{row.msgId,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      message_type = #{row.messageType,jdbcType=VARCHAR},
      source = #{row.source,jdbcType=VARCHAR},
      attachment = #{row.attachment,jdbcType=VARCHAR},
      attachment_type = #{row.attachmentType,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage">
    update agent_messages
    <set>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="attachment != null">
        attachment = #{attachment,jdbcType=VARCHAR},
      </if>
      <if test="attachmentType != null">
        attachment_type = #{attachmentType,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage">
    update agent_messages
    set msg_id = #{msgId,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      message_type = #{messageType,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      attachment = #{attachment,jdbcType=VARCHAR},
      attachment_type = #{attachmentType,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage">
    update agent_messages
    set msg_id = #{msgId,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      message_type = #{messageType,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      attachment = #{attachment,jdbcType=VARCHAR},
      attachment_type = #{attachmentType,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into agent_messages
    (id, msg_id, session_id, message_type, source, attachment, attachment_type, add_time,
      content)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.msgId,jdbcType=VARCHAR}, #{item.sessionId,jdbcType=VARCHAR},
        #{item.messageType,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR}, #{item.attachment,jdbcType=VARCHAR},
        #{item.attachmentType,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.content,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>

  <!-- 根据msg_id进行更新式插入 -->
  <insert id="insertOrUpdate" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage">
    insert into agent_messages (msg_id, session_id, message_type, source, attachment,
      attachment_type, add_time, content)
    values (#{msgId,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, #{messageType,jdbcType=VARCHAR},
      #{source,jdbcType=VARCHAR}, #{attachment,jdbcType=VARCHAR}, #{attachmentType,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{content,jdbcType=LONGVARCHAR})
    ON DUPLICATE KEY UPDATE
      session_id = VALUES(session_id),
      message_type = VALUES(message_type),
      source = VALUES(source),
      attachment = VALUES(attachment),
      attachment_type = VALUES(attachment_type),
      add_time = VALUES(add_time),
      content = VALUES(content)
  </insert>

  <!-- 批量更新式插入 -->
  <insert id="batchInsertOrUpdate" parameterType="map">
    insert into agent_messages
    (msg_id, session_id, message_type, source, attachment, attachment_type, add_time, content)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.msgId,jdbcType=VARCHAR}, #{item.sessionId,jdbcType=VARCHAR},
        #{item.messageType,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR}, #{item.attachment,jdbcType=VARCHAR},
        #{item.attachmentType,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.content,jdbcType=LONGVARCHAR})
    </foreach>
    ON DUPLICATE KEY UPDATE
      session_id = VALUES(session_id),
      message_type = VALUES(message_type),
      source = VALUES(source),
      attachment = VALUES(attachment),
      attachment_type = VALUES(attachment_type),
      add_time = VALUES(add_time),
      content = VALUES(content)
  </insert>
</mapper>