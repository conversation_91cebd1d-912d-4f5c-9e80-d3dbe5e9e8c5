<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.KnowledgeLabeledSampleMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="partition_date" jdbcType="VARCHAR" property="partitionDate" />
    <result column="agent_code" jdbcType="VARCHAR" property="agentCode" />
    <result column="sample_id" jdbcType="BIGINT" property="sampleId" />
    <result column="feature_data" jdbcType="CHAR" property="featureData" />
    <result column="label_result" jdbcType="VARCHAR" property="labelResult" />
    <result column="label_extra" jdbcType="CHAR" property="labelExtra" />
    <result column="comparison_result" jdbcType="VARCHAR" property="comparisonResult" />
    <result column="comparison_extra" jdbcType="CHAR" property="comparisonExtra" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, partition_date, agent_code, sample_id, feature_data, label_result, label_extra, 
    comparison_result, comparison_extra, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeLabeledSampleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from agent_knowledge_labeled_samples
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from agent_knowledge_labeled_samples
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from agent_knowledge_labeled_samples
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeLabeledSampleExample">
    delete from agent_knowledge_labeled_samples
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample">
    insert into agent_knowledge_labeled_samples (id, partition_date, agent_code, 
      sample_id, feature_data, label_result, 
      label_extra, comparison_result, comparison_extra, 
      add_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{partitionDate,jdbcType=VARCHAR}, #{agentCode,jdbcType=VARCHAR}, 
      #{sampleId,jdbcType=BIGINT}, #{featureData,jdbcType=CHAR}, #{labelResult,jdbcType=VARCHAR}, 
      #{labelExtra,jdbcType=CHAR}, #{comparisonResult,jdbcType=VARCHAR}, #{comparisonExtra,jdbcType=CHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample">
    insert into agent_knowledge_labeled_samples
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="partitionDate != null">
        partition_date,
      </if>
      <if test="agentCode != null">
        agent_code,
      </if>
      <if test="sampleId != null">
        sample_id,
      </if>
      <if test="featureData != null">
        feature_data,
      </if>
      <if test="labelResult != null">
        label_result,
      </if>
      <if test="labelExtra != null">
        label_extra,
      </if>
      <if test="comparisonResult != null">
        comparison_result,
      </if>
      <if test="comparisonExtra != null">
        comparison_extra,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partitionDate != null">
        #{partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="agentCode != null">
        #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null">
        #{sampleId,jdbcType=BIGINT},
      </if>
      <if test="featureData != null">
        #{featureData,jdbcType=CHAR},
      </if>
      <if test="labelResult != null">
        #{labelResult,jdbcType=VARCHAR},
      </if>
      <if test="labelExtra != null">
        #{labelExtra,jdbcType=CHAR},
      </if>
      <if test="comparisonResult != null">
        #{comparisonResult,jdbcType=VARCHAR},
      </if>
      <if test="comparisonExtra != null">
        #{comparisonExtra,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeLabeledSampleExample" resultType="java.lang.Long">
    select count(*) from agent_knowledge_labeled_samples
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update agent_knowledge_labeled_samples
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.partitionDate != null">
        partition_date = #{row.partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="row.agentCode != null">
        agent_code = #{row.agentCode,jdbcType=VARCHAR},
      </if>
      <if test="row.sampleId != null">
        sample_id = #{row.sampleId,jdbcType=BIGINT},
      </if>
      <if test="row.featureData != null">
        feature_data = #{row.featureData,jdbcType=CHAR},
      </if>
      <if test="row.labelResult != null">
        label_result = #{row.labelResult,jdbcType=VARCHAR},
      </if>
      <if test="row.labelExtra != null">
        label_extra = #{row.labelExtra,jdbcType=CHAR},
      </if>
      <if test="row.comparisonResult != null">
        comparison_result = #{row.comparisonResult,jdbcType=VARCHAR},
      </if>
      <if test="row.comparisonExtra != null">
        comparison_extra = #{row.comparisonExtra,jdbcType=CHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update agent_knowledge_labeled_samples
    set id = #{row.id,jdbcType=BIGINT},
      partition_date = #{row.partitionDate,jdbcType=VARCHAR},
      agent_code = #{row.agentCode,jdbcType=VARCHAR},
      sample_id = #{row.sampleId,jdbcType=BIGINT},
      feature_data = #{row.featureData,jdbcType=CHAR},
      label_result = #{row.labelResult,jdbcType=VARCHAR},
      label_extra = #{row.labelExtra,jdbcType=CHAR},
      comparison_result = #{row.comparisonResult,jdbcType=VARCHAR},
      comparison_extra = #{row.comparisonExtra,jdbcType=CHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample">
    update agent_knowledge_labeled_samples
    <set>
      <if test="agentCode != null">
        agent_code = #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null">
        sample_id = #{sampleId,jdbcType=BIGINT},
      </if>
      <if test="featureData != null">
        feature_data = #{featureData,jdbcType=CHAR},
      </if>
      <if test="labelResult != null">
        label_result = #{labelResult,jdbcType=VARCHAR},
      </if>
      <if test="labelExtra != null">
        label_extra = #{labelExtra,jdbcType=CHAR},
      </if>
      <if test="comparisonResult != null">
        comparison_result = #{comparisonResult,jdbcType=VARCHAR},
      </if>
      <if test="comparisonExtra != null">
        comparison_extra = #{comparisonExtra,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample">
    update agent_knowledge_labeled_samples
    set agent_code = #{agentCode,jdbcType=VARCHAR},
      sample_id = #{sampleId,jdbcType=BIGINT},
      feature_data = #{featureData,jdbcType=CHAR},
      label_result = #{labelResult,jdbcType=VARCHAR},
      label_extra = #{labelExtra,jdbcType=CHAR},
      comparison_result = #{comparisonResult,jdbcType=VARCHAR},
      comparison_extra = #{comparisonExtra,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </update>
</mapper>