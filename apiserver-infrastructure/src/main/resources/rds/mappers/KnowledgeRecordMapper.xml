<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.KnowledgeRecordMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="partition_date" jdbcType="VARCHAR" property="partitionDate" />
    <result column="agent_code" jdbcType="VARCHAR" property="agentCode" />
    <result column="collect_id" jdbcType="BIGINT" property="collectId" />
    <result column="feature_data" jdbcType="CHAR" property="featureData" />
    <result column="collect_status" jdbcType="INTEGER" property="collectStatus" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, partition_date, agent_code, collect_id, feature_data, collect_status, add_time,
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from agent_knowledge_collect_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from agent_knowledge_collect_record
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from agent_knowledge_collect_record
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeRecordExample">
    delete from agent_knowledge_collect_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord">
    insert into agent_knowledge_collect_record (id, partition_date, agent_code,
      collect_id, feature_data, collect_status,
      add_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{partitionDate,jdbcType=VARCHAR}, #{agentCode,jdbcType=VARCHAR},
      #{collectId,jdbcType=BIGINT}, #{featureData,jdbcType=CHAR}, #{collectStatus,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord">
    insert into agent_knowledge_collect_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="partitionDate != null">
        partition_date,
      </if>
      <if test="agentCode != null">
        agent_code,
      </if>
      <if test="collectId != null">
        collect_id,
      </if>
      <if test="featureData != null">
        feature_data,
      </if>
      <if test="collectStatus != null">
        collect_status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="partitionDate != null">
        #{partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="agentCode != null">
        #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="collectId != null">
        #{collectId,jdbcType=BIGINT},
      </if>
      <if test="featureData != null">
        #{featureData,jdbcType=CHAR},
      </if>
      <if test="collectStatus != null">
        #{collectStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeRecordExample" resultType="java.lang.Long">
    select count(*) from agent_knowledge_collect_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update agent_knowledge_collect_record
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.partitionDate != null">
        partition_date = #{row.partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="row.agentCode != null">
        agent_code = #{row.agentCode,jdbcType=VARCHAR},
      </if>
      <if test="row.collectId != null">
        collect_id = #{row.collectId,jdbcType=BIGINT},
      </if>
      <if test="row.featureData != null">
        feature_data = #{row.featureData,jdbcType=CHAR},
      </if>
      <if test="row.collectStatus != null">
        collect_status = #{row.collectStatus,jdbcType=INTEGER},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update agent_knowledge_collect_record
    set id = #{row.id,jdbcType=BIGINT},
      partition_date = #{row.partitionDate,jdbcType=VARCHAR},
      agent_code = #{row.agentCode,jdbcType=VARCHAR},
      collect_id = #{row.collectId,jdbcType=BIGINT},
      feature_data = #{row.featureData,jdbcType=CHAR},
      collect_status = #{row.collectStatus,jdbcType=INTEGER},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord">
    update agent_knowledge_collect_record
    <set>
      <if test="agentCode != null">
        agent_code = #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="collectId != null">
        collect_id = #{collectId,jdbcType=BIGINT},
      </if>
      <if test="featureData != null">
        feature_data = #{featureData,jdbcType=CHAR},
      </if>
      <if test="collectStatus != null">
        collect_status = #{collectStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord">
    update agent_knowledge_collect_record
    set agent_code = #{agentCode,jdbcType=VARCHAR},
      collect_id = #{collectId,jdbcType=BIGINT},
      feature_data = #{featureData,jdbcType=CHAR},
      collect_status = #{collectStatus,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
  </update>

  <!-- 分页查询collectStatus为0的记录 -->
  <select id="selectByCollectStatusZeroWithLimit" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from agent_knowledge_collect_record
    where collect_status = 0
      and partition_date = #{partitionDate,jdbcType=VARCHAR}
    order by add_time asc
    limit #{limit,jdbcType=INTEGER}
  </select>

  <!-- 批量更新记录的collectStatus -->
  <update id="batchUpdateCollectStatus">
    update agent_knowledge_collect_record
    set collect_status = #{newStatus,jdbcType=INTEGER},
        update_time = NOW()
    where (id, partition_date) in
    <foreach collection="records" item="record" open="(" separator="," close=")">
      (#{record.id,jdbcType=BIGINT}, #{record.partitionDate,jdbcType=VARCHAR})
    </foreach>
  </update>
</mapper>