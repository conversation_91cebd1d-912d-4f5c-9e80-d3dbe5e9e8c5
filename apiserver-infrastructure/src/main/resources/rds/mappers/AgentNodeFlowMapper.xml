<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentNodeFlowMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="current_node_name" jdbcType="VARCHAR" property="currentNodeName" />
    <result column="next_node_name" jdbcType="VARCHAR" property="nextNodeName" />
    <result column="current_node_status" jdbcType="VARCHAR" property="currentNodeStatus" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow">
    <result column="node_log" jdbcType="LONGVARCHAR" property="nodeLog" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, session_id, msg_id, current_node_name, next_node_name, current_node_status, add_time, 
    status
  </sql>
  <sql id="Blob_Column_List">
    node_log
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentNodeFlowExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_node_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentNodeFlowExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from agent_node_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_node_flow
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from agent_node_flow
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentNodeFlowExample">
    delete from agent_node_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow">
    insert into agent_node_flow (id, session_id, msg_id, 
      current_node_name, next_node_name, current_node_status, 
      add_time, status, node_log
      )
    values (#{id,jdbcType=BIGINT}, #{sessionId,jdbcType=VARCHAR}, #{msgId,jdbcType=VARCHAR}, 
      #{currentNodeName,jdbcType=VARCHAR}, #{nextNodeName,jdbcType=VARCHAR}, #{currentNodeStatus,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{nodeLog,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow">
    insert into agent_node_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="currentNodeName != null">
        current_node_name,
      </if>
      <if test="nextNodeName != null">
        next_node_name,
      </if>
      <if test="currentNodeStatus != null">
        current_node_status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="nodeLog != null">
        node_log,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="currentNodeName != null">
        #{currentNodeName,jdbcType=VARCHAR},
      </if>
      <if test="nextNodeName != null">
        #{nextNodeName,jdbcType=VARCHAR},
      </if>
      <if test="currentNodeStatus != null">
        #{currentNodeStatus,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="nodeLog != null">
        #{nodeLog,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentNodeFlowExample" resultType="java.lang.Long">
    select count(*) from agent_node_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update agent_node_flow
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.msgId != null">
        msg_id = #{row.msgId,jdbcType=VARCHAR},
      </if>
      <if test="row.currentNodeName != null">
        current_node_name = #{row.currentNodeName,jdbcType=VARCHAR},
      </if>
      <if test="row.nextNodeName != null">
        next_node_name = #{row.nextNodeName,jdbcType=VARCHAR},
      </if>
      <if test="row.currentNodeStatus != null">
        current_node_status = #{row.currentNodeStatus,jdbcType=VARCHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.nodeLog != null">
        node_log = #{row.nodeLog,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update agent_node_flow
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      msg_id = #{row.msgId,jdbcType=VARCHAR},
      current_node_name = #{row.currentNodeName,jdbcType=VARCHAR},
      next_node_name = #{row.nextNodeName,jdbcType=VARCHAR},
      current_node_status = #{row.currentNodeStatus,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      status = #{row.status,jdbcType=INTEGER},
      node_log = #{row.nodeLog,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update agent_node_flow
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      msg_id = #{row.msgId,jdbcType=VARCHAR},
      current_node_name = #{row.currentNodeName,jdbcType=VARCHAR},
      next_node_name = #{row.nextNodeName,jdbcType=VARCHAR},
      current_node_status = #{row.currentNodeStatus,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      status = #{row.status,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow">
    update agent_node_flow
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="currentNodeName != null">
        current_node_name = #{currentNodeName,jdbcType=VARCHAR},
      </if>
      <if test="nextNodeName != null">
        next_node_name = #{nextNodeName,jdbcType=VARCHAR},
      </if>
      <if test="currentNodeStatus != null">
        current_node_status = #{currentNodeStatus,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="nodeLog != null">
        node_log = #{nodeLog,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow">
    update agent_node_flow
    set session_id = #{sessionId,jdbcType=VARCHAR},
      msg_id = #{msgId,jdbcType=VARCHAR},
      current_node_name = #{currentNodeName,jdbcType=VARCHAR},
      next_node_name = #{nextNodeName,jdbcType=VARCHAR},
      current_node_status = #{currentNodeStatus,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      node_log = #{nodeLog,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow">
    update agent_node_flow
    set session_id = #{sessionId,jdbcType=VARCHAR},
      msg_id = #{msgId,jdbcType=VARCHAR},
      current_node_name = #{currentNodeName,jdbcType=VARCHAR},
      next_node_name = #{nextNodeName,jdbcType=VARCHAR},
      current_node_status = #{currentNodeStatus,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into agent_node_flow
    (id, session_id, msg_id, current_node_name, next_node_name, current_node_status,
      add_time, status, node_log)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.sessionId,jdbcType=VARCHAR}, #{item.msgId,jdbcType=VARCHAR},
        #{item.currentNodeName,jdbcType=VARCHAR}, #{item.nextNodeName,jdbcType=VARCHAR},
        #{item.currentNodeStatus,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.status,jdbcType=INTEGER}, #{item.nodeLog,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>

  <!-- 根据session_id, msg_id, current_node_name进行更新式插入 -->
  <insert id="insertOrUpdate" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow">
    insert into agent_node_flow (session_id, msg_id, current_node_name, next_node_name,
      current_node_status, add_time, status, node_log)
    values (#{sessionId,jdbcType=VARCHAR}, #{msgId,jdbcType=VARCHAR}, #{currentNodeName,jdbcType=VARCHAR},
      #{nextNodeName,jdbcType=VARCHAR}, #{currentNodeStatus,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=INTEGER}, #{nodeLog,jdbcType=LONGVARCHAR})
    ON DUPLICATE KEY UPDATE
      next_node_name = VALUES(next_node_name),
      current_node_status = VALUES(current_node_status),
      add_time = VALUES(add_time),
      status = VALUES(status),
      node_log = VALUES(node_log)
  </insert>

  <!-- 批量更新式插入 -->
  <insert id="batchInsertOrUpdate" parameterType="map">
    insert into agent_node_flow
    (session_id, msg_id, current_node_name, next_node_name, current_node_status,
      add_time, status, node_log)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.sessionId,jdbcType=VARCHAR}, #{item.msgId,jdbcType=VARCHAR},
        #{item.currentNodeName,jdbcType=VARCHAR}, #{item.nextNodeName,jdbcType=VARCHAR},
        #{item.currentNodeStatus,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.status,jdbcType=INTEGER}, #{item.nodeLog,jdbcType=LONGVARCHAR})
    </foreach>
    ON DUPLICATE KEY UPDATE
      next_node_name = VALUES(next_node_name),
      current_node_status = VALUES(current_node_status),
      add_time = VALUES(add_time),
      status = VALUES(status),
      node_log = VALUES(node_log)
  </insert>
</mapper>