<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentOriginalMessageMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessage">
    <id column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="model_usage" jdbcType="VARCHAR" property="modelUsage" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs">
    <result column="meta_data" jdbcType="LONGVARCHAR" property="metaData" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    msg_id, session_id, source, model_usage, type, add_time
  </sql>
  <sql id="Blob_Column_List">
    meta_data, content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentOriginalMessageExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_original_messages
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentOriginalMessageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from agent_original_messages
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from agent_original_messages
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from agent_original_messages
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentOriginalMessageExample">
    delete from agent_original_messages
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs">
    insert into agent_original_messages (msg_id, session_id, source, 
      model_usage, type, add_time, 
      meta_data, content)
    values (#{msgId,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, 
      #{modelUsage,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{metaData,jdbcType=LONGVARCHAR}, #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs">
    insert into agent_original_messages
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="modelUsage != null">
        model_usage,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="metaData != null">
        meta_data,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="modelUsage != null">
        #{modelUsage,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="metaData != null">
        #{metaData,jdbcType=LONGVARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentOriginalMessageExample" resultType="java.lang.Long">
    select count(*) from agent_original_messages
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update agent_original_messages
    <set>
      <if test="row.msgId != null">
        msg_id = #{row.msgId,jdbcType=VARCHAR},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.source != null">
        source = #{row.source,jdbcType=VARCHAR},
      </if>
      <if test="row.modelUsage != null">
        model_usage = #{row.modelUsage,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.metaData != null">
        meta_data = #{row.metaData,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update agent_original_messages
    set msg_id = #{row.msgId,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      source = #{row.source,jdbcType=VARCHAR},
      model_usage = #{row.modelUsage,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      meta_data = #{row.metaData,jdbcType=LONGVARCHAR},
      content = #{row.content,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update agent_original_messages
    set msg_id = #{row.msgId,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      source = #{row.source,jdbcType=VARCHAR},
      model_usage = #{row.modelUsage,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs">
    update agent_original_messages
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="modelUsage != null">
        model_usage = #{modelUsage,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="metaData != null">
        meta_data = #{metaData,jdbcType=LONGVARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs">
    update agent_original_messages
    set session_id = #{sessionId,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      model_usage = #{modelUsage,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      meta_data = #{metaData,jdbcType=LONGVARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessage">
    update agent_original_messages
    set session_id = #{sessionId,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      model_usage = #{modelUsage,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into agent_original_messages
    (msg_id, session_id, source, model_usage, type, add_time, meta_data, content)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.msgId,jdbcType=VARCHAR}, #{item.sessionId,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR},
        #{item.modelUsage,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.metaData,jdbcType=LONGVARCHAR}, #{item.content,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>

  <!-- 根据msg_id进行更新式插入 -->
  <insert id="insertOrUpdate" parameterType="com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs">
    insert into agent_original_messages (msg_id, session_id, source, model_usage, type, add_time,
      meta_data, content)
    values (#{msgId,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
      #{modelUsage,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP},
      #{metaData,jdbcType=LONGVARCHAR}, #{content,jdbcType=LONGVARCHAR})
    ON DUPLICATE KEY UPDATE
      session_id = VALUES(session_id),
      source = VALUES(source),
      model_usage = VALUES(model_usage),
      type = VALUES(type),
      add_time = VALUES(add_time),
      meta_data = VALUES(meta_data),
      content = VALUES(content)
  </insert>

  <!-- 批量更新式插入 -->
  <insert id="batchInsertOrUpdate" parameterType="map">
    insert into agent_original_messages
    (msg_id, session_id, source, model_usage, type, add_time, meta_data, content)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.msgId,jdbcType=VARCHAR}, #{item.sessionId,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR},
        #{item.modelUsage,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.metaData,jdbcType=LONGVARCHAR}, #{item.content,jdbcType=LONGVARCHAR})
    </foreach>
    ON DUPLICATE KEY UPDATE
      session_id = VALUES(session_id),
      source = VALUES(source),
      model_usage = VALUES(model_usage),
      type = VALUES(type),
      add_time = VALUES(add_time),
      meta_data = VALUES(meta_data),
      content = VALUES(content)
  </insert>
</mapper>