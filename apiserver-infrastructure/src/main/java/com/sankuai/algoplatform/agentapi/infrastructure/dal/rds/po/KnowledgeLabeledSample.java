package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: agent_knowledge_labeled_samples
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeLabeledSample  {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: partition_date
     *   说明: 时间分区
     */
    private String partitionDate;

    /**
     *   字段: agent_code
     *   说明: AgentCode
     */
    private String agentCode;

    /**
     *   字段: sample_id
     *   说明: 样本数据id
     */
    private Long sampleId;

    /**
     *   字段: feature_data
     *   说明: 样本特征数据
     */
    private String featureData;

    /**
     *   字段: label_result
     */
    private String labelResult;

    /**
     *   字段: label_extra
     *   说明: 人工标注其它属性
     */
    private String labelExtra;

    /**
     *   字段: comparison_result
     */
    private String comparisonResult;

    /**
     *   字段: comparison_extra
     *   说明: 对照标注其它属性
     */
    private String comparisonExtra;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}