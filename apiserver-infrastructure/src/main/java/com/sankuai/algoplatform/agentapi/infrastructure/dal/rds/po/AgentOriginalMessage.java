package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 *
 *   表名: agent_original_messages
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentOriginalMessage {
    /**
     *   字段: msg_id
     *   说明: 消息唯一标识
     */
    private String msgId;

    /**
     *   字段: session_id
     *   说明: 会话唯一标识
     */
    private String sessionId;

    /**
     *   字段: source
     *   说明: 消息来源，agent内角色
     */
    private String source;

    /**
     *   字段: model_usage
     *   说明: 模型使用信息
     */
    private String modelUsage;

    /**
     *   字段: type
     *   说明: 消息类型，agent框架内类型
     */
    private String type;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;
}