package com.sankuai.algoplatform.agentapi.infrastructure.dao;

import com.dianping.lion.common.shade.org.apache.http.client.utils.DateUtils;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeEvaluationExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.KnowledgeEvaluationMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation;
import com.sankuai.algoplatform.agentapi.infrastructure.entity.BatchUpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识评测数据访问对象
 *
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/1/2
 */
@Slf4j
@Service
public class KnowledgeEvaluationDao {

    @Resource
    private KnowledgeEvaluationMapper knowledgeEvaluationMapper;

    /**
     * 插入知识评测记录
     */
    public void insertKnowledgeEvaluation(KnowledgeEvaluation knowledgeEvaluation) {
        log.info("开始插入知识评测记录, knowledgeKey: {}, agentCode: {}",
                knowledgeEvaluation.getKnowledgeKey(), knowledgeEvaluation.getAgentCode());
        try {
            knowledgeEvaluationMapper.insert(knowledgeEvaluation);
            log.info("成功插入知识评测记录, knowledgeKey: {}, agentCode: {}",
                    knowledgeEvaluation.getKnowledgeKey(), knowledgeEvaluation.getAgentCode());
        } catch (Exception e) {
            log.error("插入知识评测记录失败, knowledgeKey: {}, agentCode: {}, error: {}",
                    knowledgeEvaluation.getKnowledgeKey(), knowledgeEvaluation.getAgentCode(), e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 根据evalStatus查询记录（带limit限制）
     */
    public List<KnowledgeEvaluation> selectByEvalStatus(Integer evalStatus, Integer limit) {
        log.info("开始根据evalStatus查询知识评测记录(带limit), evalStatus: {}, limit: {}", evalStatus, limit);
        try {
            String partitionDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            List<KnowledgeEvaluation> records = knowledgeEvaluationMapper.selectByEvalStatusWithLimit(evalStatus, partitionDate, limit);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据evalStatus查询知识评测记录(带limit), evalStatus: {}, limit: {}, 共{}条", evalStatus, limit, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据evalStatus查询知识评测记录失败(带limit), evalStatus: {}, limit: {}, error: {}", evalStatus, limit, e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 根据knowledgeKey查询记录
     */
    public List<KnowledgeEvaluation> selectByKnowledgeKey(String knowledgeKey) {
        try {
            KnowledgeEvaluationExample example = new KnowledgeEvaluationExample();
            example.createCriteria().andKnowledgeKeyEqualTo(knowledgeKey);
            example.setOrderByClause("add_time desc");

            List<KnowledgeEvaluation> records = knowledgeEvaluationMapper.selectByExample(example);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据knowledgeKey查询知识评测记录失败, knowledgeKey: {}, error: {}", knowledgeKey, e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 更新记录的evalStatus
     */
    public int updateEvalStatus(Long id, Integer newStatus) {
        log.info("开始更新知识评测记录状态, id: {}, newStatus: {}",
                id, newStatus);
        try {
            KnowledgeEvaluation record = new KnowledgeEvaluation();
            record.setId(id);
            record.setEvalStatus(newStatus);
            record.setUpdateTime(new Date());

            int updateCount = knowledgeEvaluationMapper.updateByPrimaryKeySelective(record);
            log.info("成功更新知识评测记录状态, id: {}, newStatus: {}, updateCount: {}",
                    id, newStatus, updateCount);
            return updateCount;
        } catch (Exception e) {
            log.error("更新知识评测记录状态失败, id: {}}, newStatus: {}, error: {}",
                    id, newStatus, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * updateKnowledgeEvaluation
     *
     * @return int
     */
    public int updateKnowledgeEvaluation(KnowledgeEvaluation knowledgeEvaluation) {
        log.info("开始更新知识评测记录, knowledgeEvaluation: {}", knowledgeEvaluation);
        try {
            knowledgeEvaluation.setUpdateTime(new Date());
            int updateCount = knowledgeEvaluationMapper.updateByPrimaryKeySelective(knowledgeEvaluation);
            log.info("成功更新知识评测记录, knowledgeEvaluation: {}, updateCount: {}",
                    knowledgeEvaluation, updateCount);
            return updateCount;
        } catch (Exception e) {
            log.error("更新知识评测记录失败, knowledgeEvaluation: {}, error: {}",
                    knowledgeEvaluation, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 删除记录
     */
    public int deleteById(Long id, String partitionDate) {
        log.info("开始删除知识评测记录, id: {}, partitionDate: {}", id, partitionDate);
        try {
            int deleteCount = knowledgeEvaluationMapper.deleteByPrimaryKey(id, partitionDate);
            log.info("成功删除知识评测记录, id: {}, partitionDate: {}, deleteCount: {}",
                    id, partitionDate, deleteCount);
            return deleteCount;
        } catch (Exception e) {
            log.error("删除知识评测记录失败, id: {}, partitionDate: {}, error: {}",
                    id, partitionDate, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量更新记录的evalStatus（优化版本 - 使用单个SQL）
     *
     * @param records   需要更新的记录列表
     * @param newStatus 新状态
     * @return 更新结果统计
     */
    public BatchUpdateResult updateBatchEvalStatus(List<KnowledgeEvaluation> records, Integer newStatus) {
        log.info("开始批量更新知识评测记录状态, 记录数: {}, newStatus: {}", records.size(), newStatus);

        BatchUpdateResult result = new BatchUpdateResult();

        if (CollectionUtils.isEmpty(records)) {
            log.warn("批量更新记录列表为空");
            result.setTotalCount(0);
            result.setSuccessCount(0);
            result.setFailureCount(0);
            return result;
        }

        try {
            // 提取所有记录的ID
            List<Long> ids = records.stream()
                    .map(KnowledgeEvaluation::getId)
                    .collect(Collectors.toList());

            // 使用单个SQL语句批量更新
            int updateCount = knowledgeEvaluationMapper.batchUpdateEvalStatus(ids, newStatus);

            result.setTotalCount(records.size());
            result.setSuccessCount(updateCount);
            result.setFailureCount(records.size() - updateCount);

            log.info("批量更新知识评测记录状态完成, 总数: {}, 成功: {}, 失败: {}, newStatus: {}",
                    records.size(), updateCount, records.size() - updateCount, newStatus);

            // 如果有部分记录更新失败，记录详细信息
            if (updateCount < records.size()) {
                log.warn("部分记录更新失败, 预期更新: {}, 实际更新: {}, newStatus: {}",
                        records.size(), updateCount, newStatus);
            }

        } catch (Exception e) {
            log.error("批量更新知识评测记录状态异常, 记录数: {}, newStatus: {}, error: {}",
                    records.size(), newStatus, e.getMessage(), e);

            result.setTotalCount(records.size());
            result.setSuccessCount(0);
            result.setFailureCount(records.size());
        }

        return result;
    }


    /**
     * 根据agentCode、partitionDate、evalStatus和evalResult查询记录
     */
    public List<KnowledgeEvaluation> selectByAgentCodeAndPartitionDateAndStatus(String agentCode, String partitionDate, Integer evalStatus, Integer evalResult) {
        log.info("开始根据agentCode、partitionDate、evalStatus和evalResult查询知识评测记录, agentCode: {}, partitionDate: {}, evalStatus: {}, evalResult: {}",
                agentCode, partitionDate, evalStatus, evalResult);
        try {
            KnowledgeEvaluationExample example = new KnowledgeEvaluationExample();
            example.createCriteria()
                    .andAgentCodeEqualTo(agentCode)
                    .andPartitionDateEqualTo(partitionDate)
                    .andEvalStatusEqualTo(evalStatus)
                    .andEvalResultEqualTo(evalResult);
            example.setOrderByClause("add_time desc");

            List<KnowledgeEvaluation> records = knowledgeEvaluationMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据agentCode、partitionDate、evalStatus和evalResult查询知识评测记录, agentCode: {}, partitionDate: {}, evalStatus: {}, evalResult: {}, 共{}条",
                    agentCode, partitionDate, evalStatus, evalResult, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据agentCode、partitionDate、evalStatus和evalResult查询知识评测记录失败, agentCode: {}, partitionDate: {}, evalStatus: {}, evalResult: {}, error: {}",
                    agentCode, partitionDate, evalStatus, evalResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据knowledgeKeys、evalStatus和evalResult查询记录
     */
    public List<KnowledgeEvaluation> selectByKnowledgeKeysAndStatus(List<String> knowledgeKeys, Integer evalStatus, Integer evalResult) {
        log.info("开始根据knowledgeKeys、evalStatus和evalResult查询知识评测记录, knowledgeKeys: {}, evalStatus: {}, evalResult: {}",
                knowledgeKeys, evalStatus, evalResult);
        try {
            if (CollectionUtils.isEmpty(knowledgeKeys)) {
                log.warn("knowledgeKeys为空，返回空列表");
                return Collections.emptyList();
            }

            KnowledgeEvaluationExample example = new KnowledgeEvaluationExample();
            example.createCriteria()
                    .andKnowledgeKeyIn(knowledgeKeys)
                    .andEvalStatusEqualTo(evalStatus)
                    .andEvalResultEqualTo(evalResult);
            example.setOrderByClause("add_time desc");

            List<KnowledgeEvaluation> records = knowledgeEvaluationMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据knowledgeKeys、evalStatus和evalResult查询知识评测记录, knowledgeKeys: {}, evalStatus: {}, evalResult: {}, 共{}条",
                    knowledgeKeys, evalStatus, evalResult, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据knowledgeKeys、evalStatus和evalResult查询知识评测记录失败, knowledgeKeys: {}, evalStatus: {}, evalResult: {}, error: {}",
                    knowledgeKeys, evalStatus, evalResult, e.getMessage(), e);
            throw e;
        }
    }
}