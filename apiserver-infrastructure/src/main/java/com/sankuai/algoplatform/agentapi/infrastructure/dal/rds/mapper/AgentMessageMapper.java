package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentMessageExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AgentMessageMapper extends MybatisBLOBsMapper<AgentMessage, AgentMessageExample, Long> {
    int batchInsert(@Param("list") List<AgentMessage> list);

    /**
     * 根据msg_id进行更新式插入
     * @param agentMessage 消息对象
     * @return 影响的行数
     */
    int insertOrUpdate(AgentMessage agentMessage);

    /**
     * 批量更新式插入
     * @param list 消息对象列表
     * @return 影响的行数
     */
    int batchInsertOrUpdate(@Param("list") List<AgentMessage> list);
}