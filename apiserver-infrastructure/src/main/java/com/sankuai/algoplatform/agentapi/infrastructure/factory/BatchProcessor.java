package com.sankuai.algoplatform.agentapi.infrastructure.factory;

import com.sankuai.algoplatform.agentapi.infrastructure.entity.BatchProcessConfig;
import com.sankuai.algoplatform.agentapi.infrastructure.entity.BatchProcessResult;

import java.util.List;
import java.util.function.Function;

/**
 * 批处理器接口
 * 
 * <AUTHOR>
 * @date 2025/6/24
 */
public interface BatchProcessor {
    
    /**
     * 分批并发处理数据
     * 
     * @param items 待处理的数据列表
     * @param processor 单个数据的处理函数
     * @param config 批处理配置
     * @param <T> 输入数据类型
     * @param <R> 处理结果类型
     * @return 批处理结果
     */
    <T, R> BatchProcessResult<T, R> processBatch(
            List<T> items, 
            Function<T, R> processor, 
            BatchProcessConfig config
    );
    
    /**
     * 分批并发处理数据（使用默认配置）
     * 
     * @param items 待处理的数据列表
     * @param processor 单个数据的处理函数
     * @param <T> 输入数据类型
     * @param <R> 处理结果类型
     * @return 批处理结果
     */
    default <T, R> BatchProcessResult<T, R> processBatch(List<T> items, Function<T, R> processor) {
        return processBatch(items, processor, BatchProcessConfig.builder().build());
    }
    
    /**
     * 分批并发处理数据（指定并发数）
     * 
     * @param items 待处理的数据列表
     * @param processor 单个数据的处理函数
     * @param concurrentSize 并发数
     * @param <T> 输入数据类型
     * @param <R> 处理结果类型
     * @return 批处理结果
     */
    default <T, R> BatchProcessResult<T, R> processBatch(List<T> items, Function<T, R> processor, Integer concurrentSize) {
        return processBatch(items, processor, BatchProcessConfig.builder().concurrentSize(concurrentSize).build());
    }
}