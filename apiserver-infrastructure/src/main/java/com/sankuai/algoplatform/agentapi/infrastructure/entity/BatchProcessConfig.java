package com.sankuai.algoplatform.agentapi.infrastructure.entity;

import com.google.common.util.concurrent.RateLimiter;
import lombok.Builder;
import lombok.Data;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 批处理配置类
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
@Builder
public class BatchProcessConfig {
    /**
     * 并发数，默认为30
     */
    @Builder.Default
    private Integer concurrentSize = 30;
    /**
     * 线程池，如果不指定则使用默认线程池
     */
    @Builder.Default
    private ThreadPoolExecutor threadPool = null;
    /**
     * 是否启用详细日志，默认为true
     */
    @Builder.Default
    private Boolean enableDetailLog = true;

    /**
     * 批处理名称，用于日志标识
     */
    @Builder.Default
    private String batchName = "BatchProcess";

    /**
     * 每分钟最大请求数限制，null表示不限制
     */
    private Integer maxRequestsPerMinute;

    /**
     * 限流器实例
     */
    private RateLimiter rateLimiter;

    /**
     * 获取并发数，确保不为null
     */
    public Integer getConcurrentSize() {
        return concurrentSize != null ? concurrentSize : 30;
    }


}
