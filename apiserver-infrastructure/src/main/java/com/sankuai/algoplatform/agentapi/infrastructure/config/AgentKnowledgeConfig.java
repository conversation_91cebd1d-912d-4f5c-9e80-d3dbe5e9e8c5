package com.sankuai.algoplatform.agentapi.infrastructure.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentKnowledgeConfig {

    /**
     * 测评任务名称
     */
    private String evalTask;

    /**
     * 生成知识任务名称
     */
    private String createKnowledgeTask;

    /**
     * 测评知识库Id
     */
    private Long assKnowBaseId;

    /**
     * aviator脚本
     */
    private String inputDataAviator;

    /**
     * 正式知识库Id
     */
    private Long knowBaseId;

    /**
     * 大模型appKey
     */
    private String apiKey;

    /**
    * 是否要同步到正式知识库
    */
    private Boolean syncToFormalKb;

    /**
     * 测评过程中所使用的采样数量
     */
    private Integer sampleLimt;

    /**
     * 测评批处理大小
     */
    private Integer evalBatchSize;

    /**
     * 每分钟最大请求数限制，用于限流控制
     * null或0表示不限制
     */
    private Integer maxRequestsPerMinute;

}
