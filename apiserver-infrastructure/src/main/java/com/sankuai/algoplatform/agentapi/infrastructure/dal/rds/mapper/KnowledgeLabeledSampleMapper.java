package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeLabeledSampleExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample;

public interface KnowledgeLabeledSampleMapper extends MybatisBaseMapper<KnowledgeLabeledSample, KnowledgeLabeledSampleExample,Long > {
    int deleteByPrimaryKey(Long id, String partitionDate);

    KnowledgeLabeledSample selectByPrimaryKey(Long id, String partitionDate);
}