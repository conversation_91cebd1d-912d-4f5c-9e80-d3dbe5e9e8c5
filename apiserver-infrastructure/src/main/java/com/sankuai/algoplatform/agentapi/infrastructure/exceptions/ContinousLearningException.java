package com.sankuai.algoplatform.agentapi.infrastructure.exceptions;

import lombok.Getter;

/**
 * 持续学习异常类
 * 用于处理持续学习过程中的各种异常情况
 * 
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */
public class ContinousLearningException extends Exception {

    public ContinousLearningException(String message) {
        super(message);
    }

    public ContinousLearningException(String message, Throwable e) {
        super(message, e);
    }

    public ContinousLearningException(Throwable e) {
        super(e);
    }
}