package com.sankuai.algoplatform.agentapi.infrastructure.factory;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 限流器工厂类
 * 用于创建和管理RateLimiter实例
 *
 * <AUTHOR>
 * @date 2025/7/8
 */
@Component
@Slf4j
public class RateLimiterFactory {
    
    /**
     * 创建基于令牌桶算法的限流器
     * 
     * @param maxRequestsPerMinute 每分钟最大请求数
     * @return RateLimiter实例
     */
    public RateLimiter createRateLimiter(int maxRequestsPerMinute) {
        if (maxRequestsPerMinute <= 0) {
            throw new IllegalArgumentException("每分钟最大请求数必须大于0");
        }
        
        // 将每分钟的限制转换为每秒的速率
        double permitsPerSecond = maxRequestsPerMinute / 60.0;
        
        log.info("创建限流器: 每分钟最大请求数={}, 每秒速率={}", maxRequestsPerMinute, permitsPerSecond);
        
        return RateLimiter.create(permitsPerSecond);
    }
    
    /**
     * 创建预热型限流器
     * 
     * @param maxRequestsPerMinute 每分钟最大请求数
     * @param warmupPeriodSeconds 预热时间（秒）
     * @return RateLimiter实例
     */
    public RateLimiter createWarmupRateLimiter(int maxRequestsPerMinute, int warmupPeriodSeconds) {
        if (maxRequestsPerMinute <= 0) {
            throw new IllegalArgumentException("每分钟最大请求数必须大于0");
        }
        if (warmupPeriodSeconds <= 0) {
            throw new IllegalArgumentException("预热时间必须大于0");
        }
        
        // 将每分钟的限制转换为每秒的速率
        double permitsPerSecond = maxRequestsPerMinute / 60.0;
        
        log.info("创建预热限流器: 每分钟最大请求数={}, 每秒速率={}, 预热时间={}秒", 
                maxRequestsPerMinute, permitsPerSecond, warmupPeriodSeconds);
        
        return RateLimiter.create(permitsPerSecond, warmupPeriodSeconds, java.util.concurrent.TimeUnit.SECONDS);
    }
}
