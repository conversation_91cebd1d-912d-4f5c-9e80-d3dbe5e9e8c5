package com.sankuai.algoplatform.agentapi.infrastructure.dao;

import com.dianping.lion.common.shade.org.apache.http.client.utils.DateUtils;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeLabeledSampleExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.KnowledgeLabeledSampleMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample;
import com.sankuai.algoplatform.agentapi.infrastructure.entity.BatchUpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * 知识标注样本数据访问对象
 *
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/1/2
 */
@Slf4j
@Service
public class KnowledgeLabeledSampleDao {

    @Resource
    private KnowledgeLabeledSampleMapper knowledgeLabeledSampleMapper;


    public List<KnowledgeLabeledSample> selectAllNewPartitionDateSample(String agentCode, Integer limit) {
        try {
            String partitionDate = LocalDate.now()
                    .minusDays(1)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            List<KnowledgeLabeledSample> knowledgeLabeledSamples = selectByPartitionDate(partitionDate, agentCode, limit);
            return knowledgeLabeledSamples;
        } catch (Exception e) {
            log.error("查询最新日期的知识评测记录fail error: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 插入知识标注样本记录
     */
    public void insertKnowledgeLabeledSample(KnowledgeLabeledSample knowledgeLabeledSample) {
        log.info("开始插入知识标注样本记录, sampleId: {}, agentCode: {}",
                knowledgeLabeledSample.getSampleId(), knowledgeLabeledSample.getAgentCode());
        try {
            knowledgeLabeledSampleMapper.insert(knowledgeLabeledSample);
            log.info("成功插入知识标注样本记录, sampleId: {}, agentCode: {}",
                    knowledgeLabeledSample.getSampleId(), knowledgeLabeledSample.getAgentCode());
        } catch (Exception e) {
            log.error("插入知识标注样本记录失败, sampleId: {}, agentCode: {}, error: {}",
                    knowledgeLabeledSample.getSampleId(), knowledgeLabeledSample.getAgentCode(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据ID查询记录
     */
    public KnowledgeLabeledSample selectById(Long id, String partitionDate) {
        log.info("开始根据ID查询知识标注样本记录, id: {}, partitionDate: {}", id, partitionDate);
        try {
            KnowledgeLabeledSample record = knowledgeLabeledSampleMapper.selectByPrimaryKey(id, partitionDate);
            log.info("成功根据ID查询知识标注样本记录, id: {}, partitionDate: {}, found: {}",
                    id, partitionDate, record != null);
            return record;
        } catch (Exception e) {
            log.error("根据ID查询知识标注样本记录失败, id: {}, partitionDate: {}, error: {}",
                    id, partitionDate, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据agentCode查询记录
     */
    public List<KnowledgeLabeledSample> selectByAgentCode(String agentCode) {
        log.info("开始根据agentCode查询知识标注样本记录, agentCode: {}", agentCode);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andAgentCodeEqualTo(agentCode);
            example.setOrderByClause("add_time desc");

            List<KnowledgeLabeledSample> records = knowledgeLabeledSampleMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据agentCode查询知识标注样本记录, agentCode: {}, 共{}条", agentCode, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据agentCode查询知识标注样本记录失败, agentCode: {}, error: {}", agentCode, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据sampleId查询记录
     */
    public List<KnowledgeLabeledSample> selectBySampleId(Long sampleId) {
        log.info("开始根据sampleId查询知识标注样本记录, sampleId: {}", sampleId);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andSampleIdEqualTo(sampleId);
            example.setOrderByClause("add_time desc");

            List<KnowledgeLabeledSample> records = knowledgeLabeledSampleMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据sampleId查询知识标注样本记录, sampleId: {}, 共{}条", sampleId, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据sampleId查询知识标注样本记录失败, sampleId: {}, error: {}", sampleId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据agentCode和sampleId查询记录
     */
    public List<KnowledgeLabeledSample> selectByAgentCodeAndSampleId(String agentCode, Long sampleId) {
        log.info("开始根据agentCode和sampleId查询知识标注样本记录, agentCode: {}, sampleId: {}", agentCode, sampleId);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria()
                    .andAgentCodeEqualTo(agentCode)
                    .andSampleIdEqualTo(sampleId);
            example.setOrderByClause("add_time desc");

            List<KnowledgeLabeledSample> records = knowledgeLabeledSampleMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据agentCode和sampleId查询知识标注样本记录, agentCode: {}, sampleId: {}, 共{}条",
                    agentCode, sampleId, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据agentCode和sampleId查询知识标注样本记录失败, agentCode: {}, sampleId: {}, error: {}",
                    agentCode, sampleId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据partitionDate查询记录
     */
    public List<KnowledgeLabeledSample> selectByPartitionDate(String partitionDate, String agentCode, Integer limit) {
        log.info("开始根据partitionDate查询知识标注样本记录, partitionDate: {}, agentCode: {}, limit: {}",
                partitionDate, agentCode, limit);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andAgentCodeEqualTo(agentCode).andPartitionDateEqualTo(partitionDate);

            // 设置排序和限制条件
            if (limit != null && limit > 0) {
                example.setOrderByClause("add_time desc limit " + limit);
            } else if (limit != null && limit == -1) {
                // -1表示不限制，查询所有记录
                example.setOrderByClause("add_time desc");
            }

            List<KnowledgeLabeledSample> records = knowledgeLabeledSampleMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据partitionDate查询知识标注样本记录, partitionDate: {}, agentCode: {}, 共{}条",
                    partitionDate, agentCode, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据partitionDate查询知识标注样本记录失败, partitionDate: {}, agentCode: {}, error: {}",
                    partitionDate, agentCode, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据时间范围查询记录
     */
    public List<KnowledgeLabeledSample> selectByTimeRange(Date startTime, Date endTime) {
        log.info("开始根据时间范围查询知识标注样本记录, startTime: {}, endTime: {}", startTime, endTime);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            KnowledgeLabeledSampleExample.Criteria criteria = example.createCriteria();

            if (startTime != null) {
                criteria.andAddTimeGreaterThanOrEqualTo(startTime);
            }
            if (endTime != null) {
                criteria.andAddTimeLessThanOrEqualTo(endTime);
            }
            example.setOrderByClause("add_time desc");

            List<KnowledgeLabeledSample> records = knowledgeLabeledSampleMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据时间范围查询知识标注样本记录, startTime: {}, endTime: {}, 共{}条",
                    startTime, endTime, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据时间范围查询知识标注样本记录失败, startTime: {}, endTime: {}, error: {}",
                    startTime, endTime, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据labelResult查询记录
     */
    public List<KnowledgeLabeledSample> selectByLabelResult(String labelResult) {
        log.info("开始根据labelResult查询知识标注样本记录, labelResult: {}", labelResult);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andLabelResultEqualTo(labelResult);
            example.setOrderByClause("add_time desc");

            List<KnowledgeLabeledSample> records = knowledgeLabeledSampleMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据labelResult查询知识标注样本记录, labelResult: {}, 共{}条", labelResult, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据labelResult查询知识标注样本记录失败, labelResult: {}, error: {}", labelResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据comparisonResult查询记录
     */
    public List<KnowledgeLabeledSample> selectByComparisonResult(String comparisonResult) {
        log.info("开始根据comparisonResult查询知识标注样本记录, comparisonResult: {}", comparisonResult);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andComparisonResultEqualTo(comparisonResult);
            example.setOrderByClause("add_time desc");

            List<KnowledgeLabeledSample> records = knowledgeLabeledSampleMapper.selectByExample(example);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功根据comparisonResult查询知识标注样本记录, comparisonResult: {}, 共{}条", comparisonResult, recordCount);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("根据comparisonResult查询知识标注样本记录失败, comparisonResult: {}, error: {}", comparisonResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新记录的labelResult
     */
    public int updateLabelResult(Long id, String partitionDate, String labelResult, String labelExtra) {
        log.info("开始更新知识标注样本记录标注结果, id: {}, partitionDate: {}, labelResult: {}",
                id, partitionDate, labelResult);
        try {
            KnowledgeLabeledSample record = new KnowledgeLabeledSample();
            record.setId(id);
            record.setPartitionDate(partitionDate);
            record.setLabelResult(labelResult);
            record.setLabelExtra(labelExtra);
            record.setUpdateTime(new Date());

            int updateCount = knowledgeLabeledSampleMapper.updateByPrimaryKeySelective(record);
            log.info("成功更新知识标注样本记录标注结果, id: {}, partitionDate: {}, labelResult: {}, updateCount: {}",
                    id, partitionDate, labelResult, updateCount);
            return updateCount;
        } catch (Exception e) {
            log.error("更新知识标注样本记录标注结果失败, id: {}, partitionDate: {}, labelResult: {}, error: {}",
                    id, partitionDate, labelResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新记录的comparisonResult
     */
    public int updateComparisonResult(Long id, String partitionDate, String comparisonResult, String comparisonExtra) {
        log.info("开始更新知识标注样本记录对照结果, id: {}, partitionDate: {}, comparisonResult: {}",
                id, partitionDate, comparisonResult);
        try {
            KnowledgeLabeledSample record = new KnowledgeLabeledSample();
            record.setId(id);
            record.setPartitionDate(partitionDate);
            record.setComparisonResult(comparisonResult);
            record.setComparisonExtra(comparisonExtra);
            record.setUpdateTime(new Date());

            int updateCount = knowledgeLabeledSampleMapper.updateByPrimaryKeySelective(record);
            log.info("成功更新知识标注样本记录对照结果, id: {}, partitionDate: {}, comparisonResult: {}, updateCount: {}",
                    id, partitionDate, comparisonResult, updateCount);
            return updateCount;
        } catch (Exception e) {
            log.error("更新知识标注样本记录对照结果失败, id: {}, partitionDate: {}, comparisonResult: {}, error: {}",
                    id, partitionDate, comparisonResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新记录的特征数据
     */
    public int updateFeatureData(Long id, String partitionDate, String featureData) {
        log.info("开始更新知识标注样本记录特征数据, id: {}, partitionDate: {}",
                id, partitionDate);
        try {
            KnowledgeLabeledSample record = new KnowledgeLabeledSample();
            record.setId(id);
            record.setPartitionDate(partitionDate);
            record.setFeatureData(featureData);
            record.setUpdateTime(new Date());

            int updateCount = knowledgeLabeledSampleMapper.updateByPrimaryKeySelective(record);
            log.info("成功更新知识标注样本记录特征数据, id: {}, partitionDate: {}, updateCount: {}",
                    id, partitionDate, updateCount);
            return updateCount;
        } catch (Exception e) {
            log.error("更新知识标注样本记录特征数据失败, id: {}, partitionDate: {}, error: {}",
                    id, partitionDate, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 删除记录
     */
    public int deleteById(Long id, String partitionDate) {
        log.info("开始删除知识标注样本记录, id: {}, partitionDate: {}", id, partitionDate);
        try {
            int deleteCount = knowledgeLabeledSampleMapper.deleteByPrimaryKey(id, partitionDate);
            log.info("成功删除知识标注样本记录, id: {}, partitionDate: {}, deleteCount: {}",
                    id, partitionDate, deleteCount);
            return deleteCount;
        } catch (Exception e) {
            log.error("删除知识标注样本记录失败, id: {}, partitionDate: {}, error: {}",
                    id, partitionDate, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计指定agentCode的记录数量
     */
    public long countByAgentCode(String agentCode) {
        log.info("开始统计agentCode为{}的记录数量", agentCode);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andAgentCodeEqualTo(agentCode);

            long count = knowledgeLabeledSampleMapper.countByExample(example);
            log.info("成功统计agentCode为{}的记录数量: {}", agentCode, count);
            return count;
        } catch (Exception e) {
            log.error("统计agentCode为{}的记录数量失败, error: {}", agentCode, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计指定sampleId的记录数量
     */
    public long countBySampleId(Long sampleId) {
        log.info("开始统计sampleId为{}的记录数量", sampleId);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andSampleIdEqualTo(sampleId);

            long count = knowledgeLabeledSampleMapper.countByExample(example);
            log.info("成功统计sampleId为{}的记录数量: {}", sampleId, count);
            return count;
        } catch (Exception e) {
            log.error("统计sampleId为{}的记录数量失败, error: {}", sampleId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计指定labelResult的记录数量
     */
    public long countByLabelResult(String labelResult) {
        log.info("开始统计labelResult为{}的记录数量", labelResult);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andLabelResultEqualTo(labelResult);

            long count = knowledgeLabeledSampleMapper.countByExample(example);
            log.info("成功统计labelResult为{}的记录数量: {}", labelResult, count);
            return count;
        } catch (Exception e) {
            log.error("统计labelResult为{}的记录数量失败, error: {}", labelResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计指定comparisonResult的记录数量
     */
    public long countByComparisonResult(String comparisonResult) {
        log.info("开始统计comparisonResult为{}的记录数量", comparisonResult);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria().andComparisonResultEqualTo(comparisonResult);

            long count = knowledgeLabeledSampleMapper.countByExample(example);
            log.info("成功统计comparisonResult为{}的记录数量: {}", comparisonResult, count);
            return count;
        } catch (Exception e) {
            log.error("统计comparisonResult为{}的记录数量失败, error: {}", comparisonResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据agentCode和labelResult统计记录数量
     */
    public long countByAgentCodeAndLabelResult(String agentCode, String labelResult) {
        log.info("开始统计agentCode为{}且labelResult为{}的记录数量", agentCode, labelResult);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria()
                    .andAgentCodeEqualTo(agentCode)
                    .andLabelResultEqualTo(labelResult);

            long count = knowledgeLabeledSampleMapper.countByExample(example);
            log.info("成功统计agentCode为{}且labelResult为{}的记录数量: {}", agentCode, labelResult, count);
            return count;
        } catch (Exception e) {
            log.error("统计agentCode为{}且labelResult为{}的记录数量失败, error: {}", agentCode, labelResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据agentCode和comparisonResult统计记录数量
     */
    public long countByAgentCodeAndComparisonResult(String agentCode, String comparisonResult) {
        log.info("开始统计agentCode为{}且comparisonResult为{}的记录数量", agentCode, comparisonResult);
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.createCriteria()
                    .andAgentCodeEqualTo(agentCode)
                    .andComparisonResultEqualTo(comparisonResult);

            long count = knowledgeLabeledSampleMapper.countByExample(example);
            log.info("成功统计agentCode为{}且comparisonResult为{}的记录数量: {}", agentCode, comparisonResult, count);
            return count;
        } catch (Exception e) {
            log.error("统计agentCode为{}且comparisonResult为{}的记录数量失败, error: {}", agentCode, comparisonResult, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量插入知识标注样本记录
     */
    public BatchUpdateResult batchInsert(List<KnowledgeLabeledSample> records) {
        log.info("开始批量插入知识标注样本记录, 记录数: {}", records.size());

        BatchUpdateResult result = new BatchUpdateResult();
        int successCount = 0;
        int failureCount = 0;

        for (KnowledgeLabeledSample record : records) {
            try {
                insertKnowledgeLabeledSample(record);
                successCount++;
                log.debug("成功插入记录, sampleId: {}, agentCode: {}",
                        record.getSampleId(), record.getAgentCode());
            } catch (Exception e) {
                failureCount++;
                log.error("插入记录异常, sampleId: {}, agentCode: {}, error: {}",
                        record.getSampleId(), record.getAgentCode(), e.getMessage(), e);
            }
        }

        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setTotalCount(records.size());

        log.info("批量插入知识标注样本记录完成, 总数: {}, 成功: {}, 失败: {}",
                records.size(), successCount, failureCount);

        return result;
    }

    /**
     * 批量更新记录的labelResult
     */
    public BatchUpdateResult batchUpdateLabelResult(List<KnowledgeLabeledSample> records, String labelResult, String labelExtra) {
        log.info("开始批量更新知识标注样本记录标注结果, 记录数: {}, labelResult: {}", records.size(), labelResult);

        BatchUpdateResult result = new BatchUpdateResult();
        int successCount = 0;
        int failureCount = 0;

        for (KnowledgeLabeledSample record : records) {
            try {
                int updateCount = updateLabelResult(record.getId(), record.getPartitionDate(), labelResult, labelExtra);

                if (updateCount > 0) {
                    successCount++;
                    log.debug("成功更新记录标注结果, id: {}, sampleId: {}, labelResult: {}",
                            record.getId(), record.getSampleId(), labelResult);
                } else {
                    failureCount++;
                    log.warn("更新记录标注结果失败, 影响行数为0, id: {}, sampleId: {}, labelResult: {}",
                            record.getId(), record.getSampleId(), labelResult);
                }
            } catch (Exception e) {
                failureCount++;
                log.error("更新记录标注结果异常, id: {}, sampleId: {}, labelResult: {}, error: {}",
                        record.getId(), record.getSampleId(), labelResult, e.getMessage(), e);
            }
        }

        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setTotalCount(records.size());

        log.info("批量更新知识标注样本记录标注结果完成, 总数: {}, 成功: {}, 失败: {}, labelResult: {}",
                records.size(), successCount, failureCount, labelResult);

        return result;
    }

    /**
     * 批量更新记录的comparisonResult
     */
    public BatchUpdateResult batchUpdateComparisonResult(List<KnowledgeLabeledSample> records, String comparisonResult, String comparisonExtra) {
        log.info("开始批量更新知识标注样本记录对照结果, 记录数: {}, comparisonResult: {}", records.size(), comparisonResult);

        BatchUpdateResult result = new BatchUpdateResult();
        int successCount = 0;
        int failureCount = 0;

        for (KnowledgeLabeledSample record : records) {
            try {
                int updateCount = updateComparisonResult(record.getId(), record.getPartitionDate(), comparisonResult, comparisonExtra);

                if (updateCount > 0) {
                    successCount++;
                    log.debug("成功更新记录对照结果, id: {}, sampleId: {}, comparisonResult: {}",
                            record.getId(), record.getSampleId(), comparisonResult);
                } else {
                    failureCount++;
                    log.warn("更新记录对照结果失败, 影响行数为0, id: {}, sampleId: {}, comparisonResult: {}",
                            record.getId(), record.getSampleId(), comparisonResult);
                }
            } catch (Exception e) {
                failureCount++;
                log.error("更新记录对照结果异常, id: {}, sampleId: {}, comparisonResult: {}, error: {}",
                        record.getId(), record.getSampleId(), comparisonResult, e.getMessage(), e);
            }
        }

        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setTotalCount(records.size());

        log.info("批量更新知识标注样本记录对照结果完成, 总数: {}, 成功: {}, 失败: {}, comparisonResult: {}",
                records.size(), successCount, failureCount, comparisonResult);

        return result;
    }

    /**
     * 获取最新分区日期
     */
    public String getLatestPartitionDate() {
        log.info("查询最新分区日期的知识标注样本记录");
        try {
            KnowledgeLabeledSampleExample example = new KnowledgeLabeledSampleExample();
            example.setOrderByClause("partition_date DESC");
            List<KnowledgeLabeledSample> results = knowledgeLabeledSampleMapper.selectByExample(example);

            if (CollectionUtils.isEmpty(results)) {
                // 如果没有记录，返回昨天的日期
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                return DateUtils.formatDate(calendar.getTime());
            }

            String latestPartitionDate = results.get(0).getPartitionDate();
            log.info("成功获取最新分区日期: {}", latestPartitionDate);
            return latestPartitionDate;
        } catch (Exception e) {
            log.error("获取最新分区日期失败, error: {}", e.getMessage(), e);
            throw e;
        }
    }
}