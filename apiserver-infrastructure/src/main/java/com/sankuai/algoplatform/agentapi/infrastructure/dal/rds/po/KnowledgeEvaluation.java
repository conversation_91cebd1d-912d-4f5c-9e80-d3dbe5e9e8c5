package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import java.util.Date;

import lombok.*;

/**
 * 表名: agent_knowledge_evaluation
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeEvaluation {
    /**
     * 字段: id
     * 说明: 自增主键
     */
    private Long id;

    /**
     * 字段: partition_date
     * 说明: 时间分区
     */
    private String partitionDate;

    /**
     * 字段: agent_code
     * 说明: AgentCode
     */
    private String agentCode;

    /**
     * 字段: knowledge_key
     * 说明: 知识key
     */
    private String knowledgeKey;

    /**
     * 字段: collect_id
     * 说明: 样本数据id
     */
    private Long collectId;

    /**
     * 字段: knowledge_data
     * 说明: 知识数据
     */
    private String knowledgeData;

    /**
     * 字段: eval_status
     * 说明: 状态: 0新创建，1评测中，2完成评测
     */
    private Integer evalStatus;

    /**
     * 字段: eval_result
     * 说明: 结果: 0待评测，1正向，2负向
     */
    private Integer evalResult;

    /**
     * 字段: eval_report
     * 说明: 知识评测报告
     */
    private String evalReport;

    /**
     * 字段: add_time
     * 说明: 添加时间
     */
    private Date addTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;
}