package com.sankuai.algoplatform.agentapi.infrastructure.entity;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */
public class BatchUpdateResult {
    private int totalCount;
    private int successCount;
    private int failureCount;

    // Getters and Setters
    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }

    public int getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(int failureCount) {
        this.failureCount = failureCount;
    }

    public boolean isAllSuccess() {
        return failureCount == 0;
    }

    public double getSuccessRate() {
        return totalCount == 0 ? 0.0 : (double) successCount / totalCount;
    }
}