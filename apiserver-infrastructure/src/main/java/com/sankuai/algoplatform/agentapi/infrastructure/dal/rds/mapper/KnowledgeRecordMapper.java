package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeRecordExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KnowledgeRecordMapper extends MybatisBaseMapper<KnowledgeRecord, KnowledgeRecordExample, Long> {
    KnowledgeRecord selectByPrimaryKey(Long id, String partitionDate);

    int deleteByPrimaryKey(Long id, String partitionDate);

    /**
     * 分页查询collectStatus为0的记录
     * @param partitionDate 分区日期
     * @param limit 限制条数
     * @return 记录列表
     */
    List<KnowledgeRecord> selectByCollectStatusZeroWithLimit(@Param("partitionDate") String partitionDate, @Param("limit") int limit);

    /**
     * 批量更新记录的collectStatus
     * @param records 需要更新的记录列表（包含id和partitionDate）
     * @param newStatus 新状态
     * @return 更新的记录数量
     */
    int batchUpdateCollectStatus(@Param("records") List<KnowledgeRecord> records, @Param("newStatus") Integer newStatus);
}