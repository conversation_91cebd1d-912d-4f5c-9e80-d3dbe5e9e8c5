package com.sankuai.algoplatform.agentapi.infrastructure.entity;

import lombok.Data;

import java.util.List;

/**
 * 批处理结果类
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
public class BatchProcessResult<T, R> {
    /**
     * 总处理数量
     */
    private Integer totalCount;
    /**
     * 成功数量
     */
    private Integer successCount;
    /**
     * 失败数量
     */
    private Integer failureCount;
    /**
     * 成功率
     */
    private Double successRate;
    /**
     * 处理开始时间
     */
    private Long startTime;
    /**
     * 处理结束时间
     */
    private Long endTime;
    /**
     * 处理耗时（毫秒）
     */
    private Long duration;
    /**
     * 成功的结果列表
     */
    private List<ItemProcessResult<T, R>> successResults;
    /**
     * 失败的结果列表
     */
    private List<ItemProcessResult<T, R>> failureResults;
    /**
     * 计算成功率
     */
    public void calculateSuccessRate() {
        if (totalCount != null && totalCount > 0) {
            this.successRate = (double) successCount / totalCount;
        } else {
            this.successRate = 0.0;
        }
    }
    /**
     * 计算处理耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = endTime - startTime;
        }
    }
    /**
     * 获取成功率
     * @return 成功率（0.0-1.0之间的小数）
     */
    public Double getSuccessRate() {
        return this.successRate != null ? this.successRate : 0.0;
    }

    /**
     * 单个项目处理结果
     */
    @Data
    public static class ItemProcessResult<T, R> {
        /**
         * 原始数据
         */
        private T item;
        /**
         * 处理结果
         */
        private R result;
        /**
         * 是否成功
         */
        private Boolean success;
        /**
         * 错误信息
         */
        private String errorMessage;
        /**
         * 异常对象
         */
        private Exception exception;
        /**
         * 处理耗时（毫秒）
         */
        private Long processingTime;
        public static <T, R> ItemProcessResult<T, R> success(T item, R result, Long processingTime) {
            ItemProcessResult<T, R> itemResult = new ItemProcessResult<>();
            itemResult.setItem(item);
            itemResult.setResult(result);
            itemResult.setSuccess(true);
            itemResult.setProcessingTime(processingTime);
            return itemResult;
        }
        public static <T, R> ItemProcessResult<T, R> failure(T item, String errorMessage, Exception exception, Long processingTime) {
            ItemProcessResult<T, R> itemResult = new ItemProcessResult<>();
            itemResult.setItem(item);
            itemResult.setSuccess(false);
            itemResult.setErrorMessage(errorMessage);
            itemResult.setException(exception);
            itemResult.setProcessingTime(processingTime);
            return itemResult;
        }
    }
}
