package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class KnowledgeEvaluationExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public KnowledgeEvaluationExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIsNull() {
            addCriterion("partition_date is null");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIsNotNull() {
            addCriterion("partition_date is not null");
            return (Criteria) this;
        }

        public Criteria andPartitionDateEqualTo(String value) {
            addCriterion("partition_date =", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotEqualTo(String value) {
            addCriterion("partition_date <>", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateGreaterThan(String value) {
            addCriterion("partition_date >", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateGreaterThanOrEqualTo(String value) {
            addCriterion("partition_date >=", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLessThan(String value) {
            addCriterion("partition_date <", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLessThanOrEqualTo(String value) {
            addCriterion("partition_date <=", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLike(String value) {
            addCriterion("partition_date like", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotLike(String value) {
            addCriterion("partition_date not like", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIn(List<String> values) {
            addCriterion("partition_date in", values, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotIn(List<String> values) {
            addCriterion("partition_date not in", values, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateBetween(String value1, String value2) {
            addCriterion("partition_date between", value1, value2, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotBetween(String value1, String value2) {
            addCriterion("partition_date not between", value1, value2, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andAgentCodeIsNull() {
            addCriterion("agent_code is null");
            return (Criteria) this;
        }

        public Criteria andAgentCodeIsNotNull() {
            addCriterion("agent_code is not null");
            return (Criteria) this;
        }

        public Criteria andAgentCodeEqualTo(String value) {
            addCriterion("agent_code =", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeNotEqualTo(String value) {
            addCriterion("agent_code <>", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeGreaterThan(String value) {
            addCriterion("agent_code >", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeGreaterThanOrEqualTo(String value) {
            addCriterion("agent_code >=", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeLessThan(String value) {
            addCriterion("agent_code <", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeLessThanOrEqualTo(String value) {
            addCriterion("agent_code <=", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeLike(String value) {
            addCriterion("agent_code like", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeNotLike(String value) {
            addCriterion("agent_code not like", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeIn(List<String> values) {
            addCriterion("agent_code in", values, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeNotIn(List<String> values) {
            addCriterion("agent_code not in", values, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeBetween(String value1, String value2) {
            addCriterion("agent_code between", value1, value2, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeNotBetween(String value1, String value2) {
            addCriterion("agent_code not between", value1, value2, "agentCode");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyIsNull() {
            addCriterion("knowledge_key is null");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyIsNotNull() {
            addCriterion("knowledge_key is not null");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyEqualTo(String value) {
            addCriterion("knowledge_key =", value, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyNotEqualTo(String value) {
            addCriterion("knowledge_key <>", value, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyGreaterThan(String value) {
            addCriterion("knowledge_key >", value, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyGreaterThanOrEqualTo(String value) {
            addCriterion("knowledge_key >=", value, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyLessThan(String value) {
            addCriterion("knowledge_key <", value, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyLessThanOrEqualTo(String value) {
            addCriterion("knowledge_key <=", value, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyLike(String value) {
            addCriterion("knowledge_key like", value, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyNotLike(String value) {
            addCriterion("knowledge_key not like", value, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyIn(List<String> values) {
            addCriterion("knowledge_key in", values, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyNotIn(List<String> values) {
            addCriterion("knowledge_key not in", values, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyBetween(String value1, String value2) {
            addCriterion("knowledge_key between", value1, value2, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andKnowledgeKeyNotBetween(String value1, String value2) {
            addCriterion("knowledge_key not between", value1, value2, "knowledgeKey");
            return (Criteria) this;
        }

        public Criteria andCollectIdIsNull() {
            addCriterion("collect_id is null");
            return (Criteria) this;
        }

        public Criteria andCollectIdIsNotNull() {
            addCriterion("collect_id is not null");
            return (Criteria) this;
        }

        public Criteria andCollectIdEqualTo(Long value) {
            addCriterion("collect_id =", value, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdNotEqualTo(Long value) {
            addCriterion("collect_id <>", value, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdGreaterThan(Long value) {
            addCriterion("collect_id >", value, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("collect_id >=", value, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdLessThan(Long value) {
            addCriterion("collect_id <", value, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdLessThanOrEqualTo(Long value) {
            addCriterion("collect_id <=", value, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdIn(List<Long> values) {
            addCriterion("collect_id in", values, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdNotIn(List<Long> values) {
            addCriterion("collect_id not in", values, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdBetween(Long value1, Long value2) {
            addCriterion("collect_id between", value1, value2, "collectId");
            return (Criteria) this;
        }

        public Criteria andCollectIdNotBetween(Long value1, Long value2) {
            addCriterion("collect_id not between", value1, value2, "collectId");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataIsNull() {
            addCriterion("knowledge_data is null");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataIsNotNull() {
            addCriterion("knowledge_data is not null");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataEqualTo(String value) {
            addCriterion("knowledge_data =", value, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataNotEqualTo(String value) {
            addCriterion("knowledge_data <>", value, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataGreaterThan(String value) {
            addCriterion("knowledge_data >", value, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataGreaterThanOrEqualTo(String value) {
            addCriterion("knowledge_data >=", value, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataLessThan(String value) {
            addCriterion("knowledge_data <", value, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataLessThanOrEqualTo(String value) {
            addCriterion("knowledge_data <=", value, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataLike(String value) {
            addCriterion("knowledge_data like", value, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataNotLike(String value) {
            addCriterion("knowledge_data not like", value, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataIn(List<String> values) {
            addCriterion("knowledge_data in", values, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataNotIn(List<String> values) {
            addCriterion("knowledge_data not in", values, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataBetween(String value1, String value2) {
            addCriterion("knowledge_data between", value1, value2, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andKnowledgeDataNotBetween(String value1, String value2) {
            addCriterion("knowledge_data not between", value1, value2, "knowledgeData");
            return (Criteria) this;
        }

        public Criteria andEvalStatusIsNull() {
            addCriterion("eval_status is null");
            return (Criteria) this;
        }

        public Criteria andEvalStatusIsNotNull() {
            addCriterion("eval_status is not null");
            return (Criteria) this;
        }

        public Criteria andEvalStatusEqualTo(Integer value) {
            addCriterion("eval_status =", value, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusNotEqualTo(Integer value) {
            addCriterion("eval_status <>", value, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusGreaterThan(Integer value) {
            addCriterion("eval_status >", value, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("eval_status >=", value, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusLessThan(Integer value) {
            addCriterion("eval_status <", value, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusLessThanOrEqualTo(Integer value) {
            addCriterion("eval_status <=", value, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusIn(List<Integer> values) {
            addCriterion("eval_status in", values, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusNotIn(List<Integer> values) {
            addCriterion("eval_status not in", values, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusBetween(Integer value1, Integer value2) {
            addCriterion("eval_status between", value1, value2, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("eval_status not between", value1, value2, "evalStatus");
            return (Criteria) this;
        }

        public Criteria andEvalResultIsNull() {
            addCriterion("eval_result is null");
            return (Criteria) this;
        }

        public Criteria andEvalResultIsNotNull() {
            addCriterion("eval_result is not null");
            return (Criteria) this;
        }

        public Criteria andEvalResultEqualTo(Integer value) {
            addCriterion("eval_result =", value, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultNotEqualTo(Integer value) {
            addCriterion("eval_result <>", value, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultGreaterThan(Integer value) {
            addCriterion("eval_result >", value, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("eval_result >=", value, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultLessThan(Integer value) {
            addCriterion("eval_result <", value, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultLessThanOrEqualTo(Integer value) {
            addCriterion("eval_result <=", value, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultIn(List<Integer> values) {
            addCriterion("eval_result in", values, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultNotIn(List<Integer> values) {
            addCriterion("eval_result not in", values, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultBetween(Integer value1, Integer value2) {
            addCriterion("eval_result between", value1, value2, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalResultNotBetween(Integer value1, Integer value2) {
            addCriterion("eval_result not between", value1, value2, "evalResult");
            return (Criteria) this;
        }

        public Criteria andEvalReportIsNull() {
            addCriterion("eval_report is null");
            return (Criteria) this;
        }

        public Criteria andEvalReportIsNotNull() {
            addCriterion("eval_report is not null");
            return (Criteria) this;
        }

        public Criteria andEvalReportEqualTo(String value) {
            addCriterion("eval_report =", value, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportNotEqualTo(String value) {
            addCriterion("eval_report <>", value, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportGreaterThan(String value) {
            addCriterion("eval_report >", value, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportGreaterThanOrEqualTo(String value) {
            addCriterion("eval_report >=", value, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportLessThan(String value) {
            addCriterion("eval_report <", value, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportLessThanOrEqualTo(String value) {
            addCriterion("eval_report <=", value, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportLike(String value) {
            addCriterion("eval_report like", value, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportNotLike(String value) {
            addCriterion("eval_report not like", value, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportIn(List<String> values) {
            addCriterion("eval_report in", values, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportNotIn(List<String> values) {
            addCriterion("eval_report not in", values, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportBetween(String value1, String value2) {
            addCriterion("eval_report between", value1, value2, "evalReport");
            return (Criteria) this;
        }

        public Criteria andEvalReportNotBetween(String value1, String value2) {
            addCriterion("eval_report not between", value1, value2, "evalReport");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}