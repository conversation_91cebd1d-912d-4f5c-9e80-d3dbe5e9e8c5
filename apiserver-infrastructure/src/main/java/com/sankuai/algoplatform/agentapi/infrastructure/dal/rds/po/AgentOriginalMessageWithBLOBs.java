package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 *
 *   表名: agent_original_messages
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentOriginalMessageWithBLOBs extends AgentOriginalMessage {
    /**
     *   字段: meta_data
     *   说明: 元数据
     */
    private String metaData;

    /**
     *   字段: content
     *   说明: 消息内容
     */
    private String content;
}