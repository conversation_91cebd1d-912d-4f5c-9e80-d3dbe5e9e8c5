package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: agent_states
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentState {
    /**
     *   字段: id
     *   说明: 自增ID
     */
    private Long id;

    /**
     *   字段: session_id
     *   说明: 会话唯一标识
     */
    private String sessionId;

    /**
     *   字段: version_id
     *   说明: 版本号
     */
    private String versionId;

    /**
     *   字段: add_time
     *   说明: 保存时间
     */
    private Date addTime;

    /**
     *   字段: status
     *   说明: -1 删除，0 正常
     */
    private Integer status;

    /**
     *   字段: team_state
     */
    private String teamState;
}