package com.sankuai.algoplatform.agentapi.infrastructure.dao;

import com.dianping.lion.common.shade.org.apache.http.client.utils.DateUtils;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeRecordExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.KnowledgeRecordMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord;
import com.sankuai.algoplatform.agentapi.infrastructure.entity.BatchUpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */
@Slf4j
@Service
public class KnowLedgeRecordDao {

    @Resource
    private KnowledgeRecordMapper knowledgeRecordMapper;


    /**
     * 查询所有collectStatus为0的记录（限制条数可配置）
     */
    public List<KnowledgeRecord> selectByCollectStatusZero(Integer limit) {
        log.info("开始查询collectStatus为0的知识记录, limit: {}", limit);
        try {
            // 使用Calendar计算昨天的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();
            String partitionDate = DateUtils.formatDate(yesterday, "yyyy-MM-dd");
            // 使用专门的分页查询方法，限制条数可配置
            List<KnowledgeRecord> records = knowledgeRecordMapper.selectByCollectStatusZeroWithLimit(partitionDate, limit);
            int recordCount = CollectionUtils.isEmpty(records) ? 0 : records.size();
            log.info("成功查询collectStatus为新创建的知识, 共{}条（限制{}条）", recordCount, limit);
            return records == null ? Collections.emptyList() : records;
        } catch (Exception e) {
            log.error("查询collectStatus为新创建的知识失败, limit: {}, error: {}", limit, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新记录的collectStatus
     */
    public int updateCollectStatus(Long id, String partitionDate, Integer newStatus) {
        log.info("开始更新知识记录状态, id: {}, partitionDate: {}, newStatus: {}",
                id, partitionDate, newStatus);
        try {
            KnowledgeRecord record = new KnowledgeRecord();
            record.setId(id);
            record.setPartitionDate(partitionDate);
            record.setCollectStatus(newStatus);
            record.setUpdateTime(new Date());

            int updateCount = knowledgeRecordMapper.updateByPrimaryKeySelective(record);
            log.info("成功更新知识记录状态, id: {}, partitionDate: {}, newStatus: {}, updateCount: {}",
                    id, partitionDate, newStatus, updateCount);
            return updateCount;
        } catch (Exception e) {
            log.error("更新知识记录状态失败, id: {}, partitionDate: {}, newStatus: {}, error: {}",
                    id, partitionDate, newStatus, e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 批量更新记录的collectStatus（优化版本 - 使用单个SQL）
     *
     * @param records   需要更新的记录列表
     * @param newStatus 新状态
     * @return 更新结果统计
     */
    public BatchUpdateResult updateBatchCollectStatus(List<KnowledgeRecord> records, Integer newStatus) {
        log.info("开始批量更新知识记录状态, 记录数: {}, newStatus: {}", records.size(), newStatus);

        BatchUpdateResult result = new BatchUpdateResult();

        if (CollectionUtils.isEmpty(records)) {
            log.warn("批量更新记录列表为空");
            result.setTotalCount(0);
            result.setSuccessCount(0);
            result.setFailureCount(0);
            return result;
        }

        try {
            // 使用单个SQL语句批量更新
            int updateCount = knowledgeRecordMapper.batchUpdateCollectStatus(records, newStatus);

            result.setTotalCount(records.size());
            result.setSuccessCount(updateCount);
            result.setFailureCount(records.size() - updateCount);

            log.info("批量更新知识记录状态完成, 总数: {}, 成功: {}, 失败: {}, newStatus: {}",
                    records.size(), updateCount, records.size() - updateCount, newStatus);

            // 如果有部分记录更新失败，记录详细信息
            if (updateCount < records.size()) {
                log.warn("部分记录更新失败, 预期更新: {}, 实际更新: {}, newStatus: {}",
                        records.size(), updateCount, newStatus);
            }

        } catch (Exception e) {
            log.error("批量更新知识记录状态异常, 记录数: {}, newStatus: {}, error: {}",
                    records.size(), newStatus, e.getMessage(), e);

            result.setTotalCount(records.size());
            result.setSuccessCount(0);
            result.setFailureCount(records.size());
        }

        return result;
    }


}
