package com.sankuai.algoplatform.agentapi.infrastructure.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识收集状态枚举
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */
@Getter
@AllArgsConstructor
public enum KnowledgeCollectStatusEnum {

    /**
     * 新创建
     */
    NEW_CREATED(0, "新创建"),

    /**
     * 收集中
     */
    COLLECTING(1, "收集中"),

    /**
     * 完成收集
     */
    COMPLETED(2, "完成收集"),

    /**
     * 失败
     */
    FAILED(-1, "失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static KnowledgeCollectStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (KnowledgeCollectStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为终态（完成或失败）
     * @return true表示为终态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED;
    }

    /**
     * 判断是否为成功状态
     * @return true表示为成功状态
     */
    public boolean isSuccessStatus() {
        return this == COMPLETED;
    }

    /**
     * 判断是否为失败状态
     * @return true表示为失败状态
     */
    public boolean isFailedStatus() {
        return this == FAILED;
    }
}
