package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: agent_sessions
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentSession {
    /**
     *   字段: session_id
     *   说明: 会话唯一标识
     */
    private String sessionId;

    /**
     *   字段: owner
     *   说明: 会话创建者
     */
    private String owner;

    /**
     *   字段: agent_code
     *   说明: Agent标识
     */
    private String agentCode;

    /**
     *   字段: title
     *   说明: 标题
     */
    private String title;

    /**
     *   字段: updated_at
     *   说明: 更新时间(最新消息时间)
     */
    private Date updatedAt;

    /**
     *   字段: status
     *   说明: 会话状态 0: 执行中 1: 执行成功 2: 执行失败 3: 暂停中
     */
    private Integer status;

    /**
     *   字段: show_message
     *   说明: 缩略信息
     */
    private String showMessage;
}