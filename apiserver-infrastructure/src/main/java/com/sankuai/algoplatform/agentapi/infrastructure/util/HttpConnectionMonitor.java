package com.sankuai.algoplatform.agentapi.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.pool.PoolStats;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * HTTP连接池监控工具
 * 定期监控连接池状态，提供性能指标
 * 
 * <AUTHOR>
 * @date 2025年07月10日
 */
@Component
@Slf4j
public class HttpConnectionMonitor {

    @Autowired
    private PoolingHttpClientConnectionManager connectionManager;

    /**
     * 每5分钟监控一次连接池状态
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void monitorConnectionPool() {
        try {
            PoolStats totalStats = connectionManager.getTotalStats();
            
            log.info("HTTP连接池状态监控 - " +
                    "总连接数: {}, " +
                    "可用连接数: {}, " +
                    "租借连接数: {}, " +
                    "等待连接数: {}, " +
                    "最大连接数: {}",
                    totalStats.getAvailable() + totalStats.getLeased(),
                    totalStats.getAvailable(),
                    totalStats.getLeased(),
                    totalStats.getPending(),
                    totalStats.getMax());

            // 如果可用连接数过低，发出警告
            if (totalStats.getAvailable() < 10) {
                log.warn("HTTP连接池可用连接数过低: {}, 可能影响性能", totalStats.getAvailable());
            }

            // 如果等待连接数过多，发出警告
            if (totalStats.getPending() > 20) {
                log.warn("HTTP连接池等待连接数过多: {}, 可能存在连接泄漏", totalStats.getPending());
            }

        } catch (Exception e) {
            log.error("监控HTTP连接池状态时发生错误", e);
        }
    }

    /**
     * 每小时清理一次过期连接
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanupExpiredConnections() {
        try {
            connectionManager.closeExpiredConnections();
            log.info("HTTP连接池过期连接清理完成");
        } catch (Exception e) {
            log.error("清理HTTP连接池过期连接时发生错误", e);
        }
    }

    /**
     * 获取连接池统计信息
     */
    public String getConnectionPoolStats() {
        try {
            PoolStats totalStats = connectionManager.getTotalStats();
            return String.format("连接池状态 - 总连接: %d, 可用: %d, 租借: %d, 等待: %d, 最大: %d",
                    totalStats.getAvailable() + totalStats.getLeased(),
                    totalStats.getAvailable(),
                    totalStats.getLeased(),
                    totalStats.getPending(),
                    totalStats.getMax());
        } catch (Exception e) {
            log.error("获取连接池统计信息时发生错误", e);
            return "连接池状态获取失败: " + e.getMessage();
        }
    }

    /**
     * 检查连接池健康状态
     */
    public boolean isConnectionPoolHealthy() {
        try {
            PoolStats totalStats = connectionManager.getTotalStats();
            
            // 健康检查条件：
            // 1. 可用连接数 > 5
            // 2. 等待连接数 < 50
            // 3. 租借连接数 < 最大连接数的90%
            boolean hasAvailableConnections = totalStats.getAvailable() > 5;
            boolean lowPendingConnections = totalStats.getPending() < 50;
            boolean notOverloaded = totalStats.getLeased() < (totalStats.getMax() * 0.9);
            
            boolean isHealthy = hasAvailableConnections && lowPendingConnections && notOverloaded;
            
            if (!isHealthy) {
                log.warn("HTTP连接池健康检查失败 - 可用连接: {}, 等待连接: {}, 租借连接: {}, 最大连接: {}",
                        totalStats.getAvailable(), totalStats.getPending(), 
                        totalStats.getLeased(), totalStats.getMax());
            }
            
            return isHealthy;
            
        } catch (Exception e) {
            log.error("检查连接池健康状态时发生错误", e);
            return false;
        }
    }
}
