package com.sankuai.algoplatform.agentapi.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.function.Supplier;

/**
 * HTTP重试工具类
 * 针对网络连接问题提供智能重试机制
 * 
 * <AUTHOR>
 * @date 2025年07月10日
 */
@Slf4j
public class HttpRetryUtil {

    private static final int DEFAULT_MAX_ATTEMPTS = 3;
    private static final long DEFAULT_DELAY_MS = 1000; // 1秒
    private static final long MAX_DELAY_MS = 5000; // 最大5秒

    /**
     * 带重试的HTTP请求执行
     */
    public static <T> ResponseEntity<T> executeWithRetry(
            RestTemplate restTemplate,
            String url,
            HttpMethod method,
            HttpEntity<?> requestEntity,
            Class<T> responseType) {
        
        return executeWithRetry(
                () -> restTemplate.exchange(url, method, requestEntity, responseType),
                DEFAULT_MAX_ATTEMPTS,
                DEFAULT_DELAY_MS,
                "HTTP请求"
        );
    }

    /**
     * 带重试的POST请求
     */
    public static <T> ResponseEntity<T> postWithRetry(
            RestTemplate restTemplate,
            String url,
            Object request,
            Class<T> responseType) {
        
        return executeWithRetry(
                () -> restTemplate.postForEntity(url, request, responseType),
                DEFAULT_MAX_ATTEMPTS,
                DEFAULT_DELAY_MS,
                "HTTP POST请求"
        );
    }

    /**
     * 带重试的GET请求
     */
    public static <T> ResponseEntity<T> getWithRetry(
            RestTemplate restTemplate,
            String url,
            Class<T> responseType) {
        
        return executeWithRetry(
                () -> restTemplate.getForEntity(url, responseType),
                DEFAULT_MAX_ATTEMPTS,
                DEFAULT_DELAY_MS,
                "HTTP GET请求"
        );
    }

    /**
     * 通用重试执行方法
     */
    public static <T> T executeWithRetry(
            Supplier<T> operation,
            int maxAttempts,
            long initialDelayMs,
            String operationName) {
        
        Exception lastException = null;
        long currentDelay = initialDelayMs;
        
        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                T result = operation.get();
                
                if (attempt > 1) {
                    log.info("{}重试成功 - 第{}次尝试", operationName, attempt);
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;
                
                // 判断是否需要重试
                if (!shouldRetry(e) || attempt >= maxAttempts) {
                    break;
                }
                
                log.warn("{}失败，第{}次尝试 - 错误: {}, {}ms后重试", 
                        operationName, attempt, e.getMessage(), currentDelay);
                
                // 等待后重试
                try {
                    Thread.sleep(currentDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
                
                // 指数退避，但不超过最大延迟
                currentDelay = Math.min(currentDelay * 2, MAX_DELAY_MS);
            }
        }
        
        log.error("{}最终失败 - 已尝试{}次", operationName, maxAttempts);
        throw new RuntimeException(operationName + "重试失败", lastException);
    }

    /**
     * 判断异常是否应该重试
     */
    private static boolean shouldRetry(Exception e) {
        // 网络连接相关异常应该重试
        if (e instanceof ResourceAccessException) {
            Throwable cause = e.getCause();
            return cause instanceof ConnectException ||
                   cause instanceof SocketTimeoutException ||
                   cause.getMessage().contains("Connection refused") ||
                   cause.getMessage().contains("Connection timed out") ||
                   cause.getMessage().contains("Read timed out");
        }
        
        // 其他网络相关异常
        if (e instanceof ConnectException ||
            e instanceof SocketTimeoutException) {
            return true;
        }
        
        // 检查异常消息
        String message = e.getMessage();
        if (message != null) {
            return message.contains("Connection refused") ||
                   message.contains("Connection timed out") ||
                   message.contains("Read timed out") ||
                   message.contains("Connection reset") ||
                   message.contains("No route to host");
        }
        
        return false;
    }

    /**
     * 快速重试（用于对延迟敏感的操作）
     */
    public static <T> T executeWithFastRetry(
            Supplier<T> operation,
            String operationName) {
        
        return executeWithRetry(operation, 2, 500, operationName);
    }

    /**
     * 慢重试（用于可以容忍较长延迟的操作）
     */
    public static <T> T executeWithSlowRetry(
            Supplier<T> operation,
            String operationName) {
        
        return executeWithRetry(operation, 5, 2000, operationName);
    }
}
