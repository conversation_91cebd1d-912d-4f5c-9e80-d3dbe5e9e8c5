package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: agent_messages
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentMessage {
    /**
     *   字段: id
     *   说明: 自增ID
     */
    private Long id;

    /**
     *   字段: msg_id
     *   说明: 消息唯一标识
     */
    private String msgId;

    /**
     *   字段: session_id
     *   说明: 归属会话
     */
    private String sessionId;

    /**
     *   字段: message_type
     *   说明: 消息类型
     */
    private String messageType;

    /**
     *   字段: source
     *   说明: 消息来源, agent内角色名称
     */
    private String source;

    /**
     *   字段: attachment
     *   说明: 附件链接，逗号拼接
     */
    private String attachment;

    /**
     *   字段: attachment_type
     *   说明: 附件类型
     */
    private String attachmentType;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: content
     *   说明: 消息内容
     */
    private String content;
}