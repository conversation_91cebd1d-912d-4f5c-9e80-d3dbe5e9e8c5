package com.sankuai.algoplatform.agentapi.infrastructure.util;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/26
 */

import com.dianping.lion.client.util.StringUtils;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ClassificationMetrics {

    /**
     * 计算二分类评估指标
     * @param yTrue 真实标签列表 (0为负类，1为正类)
     * @param yPred 预测标签列表 (0为负类，1为正类)
     * @return 包含各项指标的Map
     */
    public static Map<String, Double> calculateBinaryMetrics(List<Integer> yTrue, List<Integer> yPred) {
        if (yTrue == null || yPred == null) {
            throw new IllegalArgumentException("真实标签和预测标签不能为空");
        }
        if (yTrue.size() != yPred.size()) {
            throw new IllegalArgumentException("真实标签和预测标签数量必须相同");
        }

        // 初始化混淆矩阵
        int tp = 0;  // 真正例：实际为1，预测为1
        int tn = 0;  // 真负例：实际为0，预测为0
        int fp = 0;  // 假正例：实际为0，预测为1
        int fn = 0;  // 假负例：实际为1，预测为0

        // 统计混淆矩阵
        for (int i = 0; i < yTrue.size(); i++) {
            Integer trueLabel = yTrue.get(i);
            Integer predLabel = yPred.get(i);

            // 检查null值
            if (trueLabel == null || predLabel == null) {
            //    log.warn("发现null值: 位置{}, trueLabel={}, predLabel={}", i, trueLabel, predLabel);
                continue; // 跳过null值
            }

            if (trueLabel == 1 && predLabel == 1) {
                tp++;
            } else if (trueLabel == 0 && predLabel == 0) {
                tn++;
            } else if (trueLabel == 0 && predLabel == 1) {
                fp++;
            } else if (trueLabel == 1 && predLabel == 0) {
                fn++;
            }
        }

        // 计算各项指标
        Map<String, Double> results = new HashMap<>();

        // 准确率 (Accuracy)
        double accuracy = (tp + tn + fp + fn > 0) ? (double) (tp + tn) / (tp + tn + fp + fn) : 0.0;
        results.put("Accuracy", accuracy);

        // 精确率 (Precision)
        double precision = (tp + fp > 0) ? (double) tp / (tp + fp) : 0.0;
        results.put("Precision", precision);

        // 召回率 (Recall/Sensitivity)
        double recall = (tp + fn > 0) ? (double) tp / (tp + fn) : 0.0;
        results.put("Recall", recall);

        // F1分数
        double f1 = (precision + recall > 0) ?
                2 * (precision * recall) / (precision + recall) : 0.0;
        results.put("F1_Score", f1);

        results.put("TP", (double) tp);
        results.put("TN", (double) tn);
        results.put("FP", (double) fp);
        results.put("FN", (double) fn);
        return results;
    }

    public static Double caculeteOnlineF1Score(List<KnowledgeLabeledSample> knowledgeLabeledSamples) {
        List<Integer> yTrueList = new ArrayList<>();
        List<Integer> yPredList = new ArrayList<>();
        for (KnowledgeLabeledSample knowledgeLabeledSample : knowledgeLabeledSamples) {
            String labelResult = knowledgeLabeledSample.getLabelResult();
            String comparisonResult = knowledgeLabeledSample.getComparisonResult();

            // 跳过null值或空字符串
            if (StringUtils.isBlank(labelResult) || StringUtils.isBlank(comparisonResult)) {
                log.warn("发现null或空的标签值, sampleId: {}, labelResult: {}, comparisonResult: {}",
                        knowledgeLabeledSample.getSampleId(), labelResult, comparisonResult);
                continue;
            }

            try {
                yTrueList.add(Integer.valueOf(labelResult));
                yPredList.add(Integer.valueOf(comparisonResult));
            } catch (NumberFormatException e) {
                log.warn("标签值转换为数字失败, sampleId: {}, labelResult: {}, comparisonResult: {}",
                        knowledgeLabeledSample.getSampleId(), labelResult, comparisonResult);
                continue;
            }
        }

        if (yTrueList.isEmpty()) {
            log.warn("没有有效的标签数据用于计算F1分数");
            return 0.0;
        }

        return ClassificationMetrics.calculateBinaryMetrics(yTrueList, yPredList).get("F1_Score");
    }
}

