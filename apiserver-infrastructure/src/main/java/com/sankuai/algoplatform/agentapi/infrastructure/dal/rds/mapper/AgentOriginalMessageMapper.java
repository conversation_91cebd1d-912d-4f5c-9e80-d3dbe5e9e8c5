package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentOriginalMessageExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessage;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AgentOriginalMessageMapper extends MybatisBLOBsMapper<AgentOriginalMessage, AgentOriginalMessageExample, String> {
    int batchInsert(@Param("list") List<com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs> list);

    /**
     * 根据msg_id进行更新式插入
     * @param agentOriginalMessage 原始消息对象
     * @return 影响的行数
     */
    int insertOrUpdate(com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs agentOriginalMessage);

    /**
     * 批量更新式插入
     * @param list 原始消息对象列表
     * @return 影响的行数
     */
    int batchInsertOrUpdate(@Param("list") List<com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs> list);
}