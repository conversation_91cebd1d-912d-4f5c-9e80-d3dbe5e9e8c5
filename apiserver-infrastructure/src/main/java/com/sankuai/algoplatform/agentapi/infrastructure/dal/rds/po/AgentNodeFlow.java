package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: agent_node_flow
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentNodeFlow {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: session_id
     *   说明: 会话唯一标识
     */
    private String sessionId;

    /**
     *   字段: msg_id
     *   说明: 消息唯一标识
     */
    private String msgId;

    /**
     *   字段: current_node_name
     *   说明: 当前节点名称
     */
    private String currentNodeName;

    /**
     *   字段: next_node_name
     *   说明: 下一个节点名称
     */
    private String nextNodeName;

    /**
     *   字段: current_node_status
     *   说明: 当前节点状态
     */
    private String currentNodeStatus;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: status
     *   说明: 状态 -1 删除 0 正常
     */
    private Integer status;

    /**
     *   字段: node_log
     *   说明: 节点日志
     */
    private String nodeLog;
}