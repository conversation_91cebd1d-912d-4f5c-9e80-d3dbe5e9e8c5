package com.sankuai.algoplatform.agentapi.infrastructure.util;

import com.cip.crane.netty.utils.Pair;
import com.dianping.cat.Cat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.BiFunction;
import java.util.function.Function;

public class RetryUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(RetryUtil.class);

    public static <R> R retry(Supplier<R> supplier, BiFunction<R, Exception, Boolean> getNeedRetry,
                              int maxAttempts, long delay) {
        Exception ex = null;
        R res = null;
        for (int i = 0; i < maxAttempts; i++) {
            // 执行
            try {
                res = supplier.get();
                ex = null;
            } catch (Exception e) {
                ex = e;
                res = null;
            }
            // 是否需要重试
            boolean needRetry = getNeedRetry.apply(res, ex);
            if (!needRetry) {
                return res;
            }
            // 重试时长(最后一次不等待)
            if (i < maxAttempts - 1) {
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    LOGGER.error("retry error", e);
                    Cat.logError(e);
                }
            }
        }
        if (ex != null) {
            LOGGER.error("retry error", ex);
            throw new RuntimeException(ex);
        }
        return res;
    }

    public static void retry(Runnable runnable, Function<Exception, Boolean> getNeedRetry,
                             int maxAttempts, long delay) {
        Exception ex = null;
        for (int i = 0; i < maxAttempts; i++) {
            // 执行
            try {
                runnable.run();
                ex = null;
            } catch (Exception e) {
                LOGGER.error("retry error", e);
                ex = e;
            }
            // 是否需要重试
            boolean needRetry = getNeedRetry.apply(ex);
            if (!needRetry) {
                return;
            }
            // 重试时长(最后一次不等待)
            if (i < maxAttempts - 1) {
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    LOGGER.error("retry error", e);
                    e.printStackTrace();
                }
            }
        }
        if (ex != null) {
            LOGGER.error("retry error", ex);
            throw new RuntimeException(ex);
        }
    }

    @FunctionalInterface
    public interface Supplier<T> {
        T get() throws Exception;
    }

    @FunctionalInterface
    public interface Runnable {
        void run() throws Exception;
    }
}

