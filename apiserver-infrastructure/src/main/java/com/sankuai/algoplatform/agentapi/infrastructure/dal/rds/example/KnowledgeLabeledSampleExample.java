package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class KnowledgeLabeledSampleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public KnowledgeLabeledSampleExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIsNull() {
            addCriterion("partition_date is null");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIsNotNull() {
            addCriterion("partition_date is not null");
            return (Criteria) this;
        }

        public Criteria andPartitionDateEqualTo(String value) {
            addCriterion("partition_date =", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotEqualTo(String value) {
            addCriterion("partition_date <>", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateGreaterThan(String value) {
            addCriterion("partition_date >", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateGreaterThanOrEqualTo(String value) {
            addCriterion("partition_date >=", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLessThan(String value) {
            addCriterion("partition_date <", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLessThanOrEqualTo(String value) {
            addCriterion("partition_date <=", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLike(String value) {
            addCriterion("partition_date like", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotLike(String value) {
            addCriterion("partition_date not like", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIn(List<String> values) {
            addCriterion("partition_date in", values, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotIn(List<String> values) {
            addCriterion("partition_date not in", values, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateBetween(String value1, String value2) {
            addCriterion("partition_date between", value1, value2, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotBetween(String value1, String value2) {
            addCriterion("partition_date not between", value1, value2, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andAgentCodeIsNull() {
            addCriterion("agent_code is null");
            return (Criteria) this;
        }

        public Criteria andAgentCodeIsNotNull() {
            addCriterion("agent_code is not null");
            return (Criteria) this;
        }

        public Criteria andAgentCodeEqualTo(String value) {
            addCriterion("agent_code =", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeNotEqualTo(String value) {
            addCriterion("agent_code <>", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeGreaterThan(String value) {
            addCriterion("agent_code >", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeGreaterThanOrEqualTo(String value) {
            addCriterion("agent_code >=", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeLessThan(String value) {
            addCriterion("agent_code <", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeLessThanOrEqualTo(String value) {
            addCriterion("agent_code <=", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeLike(String value) {
            addCriterion("agent_code like", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeNotLike(String value) {
            addCriterion("agent_code not like", value, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeIn(List<String> values) {
            addCriterion("agent_code in", values, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeNotIn(List<String> values) {
            addCriterion("agent_code not in", values, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeBetween(String value1, String value2) {
            addCriterion("agent_code between", value1, value2, "agentCode");
            return (Criteria) this;
        }

        public Criteria andAgentCodeNotBetween(String value1, String value2) {
            addCriterion("agent_code not between", value1, value2, "agentCode");
            return (Criteria) this;
        }

        public Criteria andSampleIdIsNull() {
            addCriterion("sample_id is null");
            return (Criteria) this;
        }

        public Criteria andSampleIdIsNotNull() {
            addCriterion("sample_id is not null");
            return (Criteria) this;
        }

        public Criteria andSampleIdEqualTo(Long value) {
            addCriterion("sample_id =", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdNotEqualTo(Long value) {
            addCriterion("sample_id <>", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdGreaterThan(Long value) {
            addCriterion("sample_id >", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sample_id >=", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdLessThan(Long value) {
            addCriterion("sample_id <", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdLessThanOrEqualTo(Long value) {
            addCriterion("sample_id <=", value, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdIn(List<Long> values) {
            addCriterion("sample_id in", values, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdNotIn(List<Long> values) {
            addCriterion("sample_id not in", values, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdBetween(Long value1, Long value2) {
            addCriterion("sample_id between", value1, value2, "sampleId");
            return (Criteria) this;
        }

        public Criteria andSampleIdNotBetween(Long value1, Long value2) {
            addCriterion("sample_id not between", value1, value2, "sampleId");
            return (Criteria) this;
        }

        public Criteria andFeatureDataIsNull() {
            addCriterion("feature_data is null");
            return (Criteria) this;
        }

        public Criteria andFeatureDataIsNotNull() {
            addCriterion("feature_data is not null");
            return (Criteria) this;
        }

        public Criteria andFeatureDataEqualTo(String value) {
            addCriterion("feature_data =", value, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataNotEqualTo(String value) {
            addCriterion("feature_data <>", value, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataGreaterThan(String value) {
            addCriterion("feature_data >", value, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataGreaterThanOrEqualTo(String value) {
            addCriterion("feature_data >=", value, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataLessThan(String value) {
            addCriterion("feature_data <", value, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataLessThanOrEqualTo(String value) {
            addCriterion("feature_data <=", value, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataLike(String value) {
            addCriterion("feature_data like", value, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataNotLike(String value) {
            addCriterion("feature_data not like", value, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataIn(List<String> values) {
            addCriterion("feature_data in", values, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataNotIn(List<String> values) {
            addCriterion("feature_data not in", values, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataBetween(String value1, String value2) {
            addCriterion("feature_data between", value1, value2, "featureData");
            return (Criteria) this;
        }

        public Criteria andFeatureDataNotBetween(String value1, String value2) {
            addCriterion("feature_data not between", value1, value2, "featureData");
            return (Criteria) this;
        }

        public Criteria andLabelResultIsNull() {
            addCriterion("label_result is null");
            return (Criteria) this;
        }

        public Criteria andLabelResultIsNotNull() {
            addCriterion("label_result is not null");
            return (Criteria) this;
        }

        public Criteria andLabelResultEqualTo(String value) {
            addCriterion("label_result =", value, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultNotEqualTo(String value) {
            addCriterion("label_result <>", value, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultGreaterThan(String value) {
            addCriterion("label_result >", value, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultGreaterThanOrEqualTo(String value) {
            addCriterion("label_result >=", value, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultLessThan(String value) {
            addCriterion("label_result <", value, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultLessThanOrEqualTo(String value) {
            addCriterion("label_result <=", value, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultLike(String value) {
            addCriterion("label_result like", value, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultNotLike(String value) {
            addCriterion("label_result not like", value, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultIn(List<String> values) {
            addCriterion("label_result in", values, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultNotIn(List<String> values) {
            addCriterion("label_result not in", values, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultBetween(String value1, String value2) {
            addCriterion("label_result between", value1, value2, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelResultNotBetween(String value1, String value2) {
            addCriterion("label_result not between", value1, value2, "labelResult");
            return (Criteria) this;
        }

        public Criteria andLabelExtraIsNull() {
            addCriterion("label_extra is null");
            return (Criteria) this;
        }

        public Criteria andLabelExtraIsNotNull() {
            addCriterion("label_extra is not null");
            return (Criteria) this;
        }

        public Criteria andLabelExtraEqualTo(String value) {
            addCriterion("label_extra =", value, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraNotEqualTo(String value) {
            addCriterion("label_extra <>", value, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraGreaterThan(String value) {
            addCriterion("label_extra >", value, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraGreaterThanOrEqualTo(String value) {
            addCriterion("label_extra >=", value, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraLessThan(String value) {
            addCriterion("label_extra <", value, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraLessThanOrEqualTo(String value) {
            addCriterion("label_extra <=", value, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraLike(String value) {
            addCriterion("label_extra like", value, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraNotLike(String value) {
            addCriterion("label_extra not like", value, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraIn(List<String> values) {
            addCriterion("label_extra in", values, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraNotIn(List<String> values) {
            addCriterion("label_extra not in", values, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraBetween(String value1, String value2) {
            addCriterion("label_extra between", value1, value2, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andLabelExtraNotBetween(String value1, String value2) {
            addCriterion("label_extra not between", value1, value2, "labelExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonResultIsNull() {
            addCriterion("comparison_result is null");
            return (Criteria) this;
        }

        public Criteria andComparisonResultIsNotNull() {
            addCriterion("comparison_result is not null");
            return (Criteria) this;
        }

        public Criteria andComparisonResultEqualTo(String value) {
            addCriterion("comparison_result =", value, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultNotEqualTo(String value) {
            addCriterion("comparison_result <>", value, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultGreaterThan(String value) {
            addCriterion("comparison_result >", value, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultGreaterThanOrEqualTo(String value) {
            addCriterion("comparison_result >=", value, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultLessThan(String value) {
            addCriterion("comparison_result <", value, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultLessThanOrEqualTo(String value) {
            addCriterion("comparison_result <=", value, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultLike(String value) {
            addCriterion("comparison_result like", value, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultNotLike(String value) {
            addCriterion("comparison_result not like", value, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultIn(List<String> values) {
            addCriterion("comparison_result in", values, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultNotIn(List<String> values) {
            addCriterion("comparison_result not in", values, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultBetween(String value1, String value2) {
            addCriterion("comparison_result between", value1, value2, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonResultNotBetween(String value1, String value2) {
            addCriterion("comparison_result not between", value1, value2, "comparisonResult");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraIsNull() {
            addCriterion("comparison_extra is null");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraIsNotNull() {
            addCriterion("comparison_extra is not null");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraEqualTo(String value) {
            addCriterion("comparison_extra =", value, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraNotEqualTo(String value) {
            addCriterion("comparison_extra <>", value, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraGreaterThan(String value) {
            addCriterion("comparison_extra >", value, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraGreaterThanOrEqualTo(String value) {
            addCriterion("comparison_extra >=", value, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraLessThan(String value) {
            addCriterion("comparison_extra <", value, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraLessThanOrEqualTo(String value) {
            addCriterion("comparison_extra <=", value, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraLike(String value) {
            addCriterion("comparison_extra like", value, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraNotLike(String value) {
            addCriterion("comparison_extra not like", value, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraIn(List<String> values) {
            addCriterion("comparison_extra in", values, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraNotIn(List<String> values) {
            addCriterion("comparison_extra not in", values, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraBetween(String value1, String value2) {
            addCriterion("comparison_extra between", value1, value2, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andComparisonExtraNotBetween(String value1, String value2) {
            addCriterion("comparison_extra not between", value1, value2, "comparisonExtra");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}