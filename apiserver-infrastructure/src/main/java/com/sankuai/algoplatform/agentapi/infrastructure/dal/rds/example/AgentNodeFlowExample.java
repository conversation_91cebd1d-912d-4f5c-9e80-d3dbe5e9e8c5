package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AgentNodeFlowExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public AgentNodeFlowExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public AgentNodeFlowExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public AgentNodeFlowExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public AgentNodeFlowExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(String value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(String value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(String value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(String value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(String value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(String value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLike(String value) {
            addCriterion("session_id like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLike(String value) {
            addCriterion("session_id not like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<String> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<String> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(String value1, String value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(String value1, String value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andMsgIdIsNull() {
            addCriterion("msg_id is null");
            return (Criteria) this;
        }

        public Criteria andMsgIdIsNotNull() {
            addCriterion("msg_id is not null");
            return (Criteria) this;
        }

        public Criteria andMsgIdEqualTo(String value) {
            addCriterion("msg_id =", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdNotEqualTo(String value) {
            addCriterion("msg_id <>", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdGreaterThan(String value) {
            addCriterion("msg_id >", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdGreaterThanOrEqualTo(String value) {
            addCriterion("msg_id >=", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdLessThan(String value) {
            addCriterion("msg_id <", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdLessThanOrEqualTo(String value) {
            addCriterion("msg_id <=", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdLike(String value) {
            addCriterion("msg_id like", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdNotLike(String value) {
            addCriterion("msg_id not like", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdIn(List<String> values) {
            addCriterion("msg_id in", values, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdNotIn(List<String> values) {
            addCriterion("msg_id not in", values, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdBetween(String value1, String value2) {
            addCriterion("msg_id between", value1, value2, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdNotBetween(String value1, String value2) {
            addCriterion("msg_id not between", value1, value2, "msgId");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameIsNull() {
            addCriterion("current_node_name is null");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameIsNotNull() {
            addCriterion("current_node_name is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameEqualTo(String value) {
            addCriterion("current_node_name =", value, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameNotEqualTo(String value) {
            addCriterion("current_node_name <>", value, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameGreaterThan(String value) {
            addCriterion("current_node_name >", value, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameGreaterThanOrEqualTo(String value) {
            addCriterion("current_node_name >=", value, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameLessThan(String value) {
            addCriterion("current_node_name <", value, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameLessThanOrEqualTo(String value) {
            addCriterion("current_node_name <=", value, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameLike(String value) {
            addCriterion("current_node_name like", value, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameNotLike(String value) {
            addCriterion("current_node_name not like", value, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameIn(List<String> values) {
            addCriterion("current_node_name in", values, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameNotIn(List<String> values) {
            addCriterion("current_node_name not in", values, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameBetween(String value1, String value2) {
            addCriterion("current_node_name between", value1, value2, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeNameNotBetween(String value1, String value2) {
            addCriterion("current_node_name not between", value1, value2, "currentNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameIsNull() {
            addCriterion("next_node_name is null");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameIsNotNull() {
            addCriterion("next_node_name is not null");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameEqualTo(String value) {
            addCriterion("next_node_name =", value, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameNotEqualTo(String value) {
            addCriterion("next_node_name <>", value, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameGreaterThan(String value) {
            addCriterion("next_node_name >", value, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameGreaterThanOrEqualTo(String value) {
            addCriterion("next_node_name >=", value, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameLessThan(String value) {
            addCriterion("next_node_name <", value, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameLessThanOrEqualTo(String value) {
            addCriterion("next_node_name <=", value, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameLike(String value) {
            addCriterion("next_node_name like", value, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameNotLike(String value) {
            addCriterion("next_node_name not like", value, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameIn(List<String> values) {
            addCriterion("next_node_name in", values, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameNotIn(List<String> values) {
            addCriterion("next_node_name not in", values, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameBetween(String value1, String value2) {
            addCriterion("next_node_name between", value1, value2, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andNextNodeNameNotBetween(String value1, String value2) {
            addCriterion("next_node_name not between", value1, value2, "nextNodeName");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusIsNull() {
            addCriterion("current_node_status is null");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusIsNotNull() {
            addCriterion("current_node_status is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusEqualTo(String value) {
            addCriterion("current_node_status =", value, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusNotEqualTo(String value) {
            addCriterion("current_node_status <>", value, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusGreaterThan(String value) {
            addCriterion("current_node_status >", value, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusGreaterThanOrEqualTo(String value) {
            addCriterion("current_node_status >=", value, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusLessThan(String value) {
            addCriterion("current_node_status <", value, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusLessThanOrEqualTo(String value) {
            addCriterion("current_node_status <=", value, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusLike(String value) {
            addCriterion("current_node_status like", value, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusNotLike(String value) {
            addCriterion("current_node_status not like", value, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusIn(List<String> values) {
            addCriterion("current_node_status in", values, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusNotIn(List<String> values) {
            addCriterion("current_node_status not in", values, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusBetween(String value1, String value2) {
            addCriterion("current_node_status between", value1, value2, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andCurrentNodeStatusNotBetween(String value1, String value2) {
            addCriterion("current_node_status not between", value1, value2, "currentNodeStatus");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}