package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: agent_knowledge_collect_record
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeRecord {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: partition_date
     *   说明: 时间分区
     */
    private String partitionDate;

    /**
     *   字段: agent_code
     *   说明: AgentCode
     */
    private String agentCode;

    /**
     *   字段: collect_id
     *   说明: 样本数据id
     */
    private Long collectId;

    /**
     *   字段: feature_data
     *   说明: 样本特征数据
     */
    private String featureData;

    /**
     *   字段: collect_status
     *   说明: 状态: 0新创建，1收集中，2完成收集，-1失败
     */
    private Integer collectStatus;

    /**
     *   字段: add_time
     *   说明: 添加时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}