package com.sankuai.algoplatform.agentapi.infrastructure.factory;

import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.util.concurrent.RateLimiter;
import com.sankuai.algoplatform.agentapi.infrastructure.entity.BatchProcessConfig;
import com.sankuai.algoplatform.agentapi.infrastructure.entity.BatchProcessResult;
import com.sankuai.algoplatform.agentapi.infrastructure.entity.BatchProcessResult.ItemProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * 默认批处理器实现
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Component
@Slf4j
public class DefaultBatchProcessor implements BatchProcessor {

    @Resource
    private ThreadPoolFactory threadPoolFactory;

    private static final int DEFAULT_CONCURRENT_SIZE = 5;

    @Override
    public <T, R> BatchProcessResult<T, R> processBatch(List<T> items, Function<T, R> processor, BatchProcessConfig config) {
        if (CollectionUtils.isEmpty(items)) {
            log.info("{}：没有需要处理的数据", config.getBatchName());
            return createEmptyResult();
        }

        long startTime = System.currentTimeMillis();
        BatchProcessResult<T, R> result = new BatchProcessResult<>();
        result.setTotalCount(items.size());
        result.setStartTime(startTime);
        result.setSuccessResults(new ArrayList<>());
        result.setFailureResults(new ArrayList<>());

        ThreadPoolExecutor threadPool = null;
        try {
            // 获取线程池
            threadPool = config.getThreadPool() == null ? threadPoolFactory.getDefaultThreadPool() : config.getThreadPool();
            // 分批处理
            processBatchesSequentially(items, processor, config, threadPool, result);
        } catch (Exception e) {
            log.error("{}：批处理执行失败", config.getBatchName(), e);
        } finally {
            // 计算最终结果
            long endTime = System.currentTimeMillis();
            result.setEndTime(endTime);
            result.calculateDuration();
            result.setSuccessCount(result.getSuccessResults().size());
            result.setFailureCount(result.getFailureResults().size());
            result.calculateSuccessRate();
            log.info("{}：批处理完成，总数: {}, 成功: {}, 失败: {}, 成功率: {}, 耗时: {}ms",
                    config.getBatchName(), result.getTotalCount(), result.getSuccessCount(),
                    result.getFailureCount(), result.getSuccessRate() * 100, result.getDuration());
        }
        return result;
    }

    /**
     * 按批次顺序处理数据
     */
    private <T, R> void processBatchesSequentially(List<T> items, Function<T, R> processor,
                                                   BatchProcessConfig config, ThreadPoolExecutor threadPool,
                                                   BatchProcessResult<T, R> result) {
        int totalSize = items.size();
        Integer concurrentSizeObj = config.getConcurrentSize();
        // 防护：如果并发数为null，使用默认值5
        int concurrentSize = concurrentSizeObj != null ? concurrentSizeObj : DEFAULT_CONCURRENT_SIZE;
        int batchCount = (totalSize + concurrentSize - 1) / concurrentSize; // 向上取整

        if (config.getEnableDetailLog()) {
            log.info("{}：开始分批处理，总数: {}, 并发数: {}, 分批数: {}",
                    config.getBatchName(), totalSize, concurrentSize, batchCount);
        }

        for (int batchIndex = 0; batchIndex < batchCount; batchIndex++) {
            int startIndex = batchIndex * concurrentSize;
            int endIndex = Math.min(startIndex + concurrentSize, totalSize);

            List<T> batchItems = items.subList(startIndex, endIndex);

            if (config.getEnableDetailLog()) {
                log.info("{}：开始处理第{}批，范围: [{}, {}), 本批数量: {},进度:{}/{}",
                        config.getBatchName(), batchIndex + 1, startIndex, endIndex, batchItems.size(), batchIndex + 1, batchCount);
            }

            // 处理当前批次
            processSingleBatch(batchItems, processor, config, threadPool, result, batchIndex + 1);
        }
    }

    /**
     * 处理单个批次
     */
    private <T, R> void processSingleBatch(List<T> batchItems, Function<T, R> processor,
                                           BatchProcessConfig config, ThreadPoolExecutor threadPool,
                                           BatchProcessResult<T, R> result, int batchNumber) {

        List<CompletableFuture<ItemProcessResult<T, R>>> futures = new ArrayList<>();

        // 并行处理每个项目
        for (T item : batchItems) {
            CompletableFuture<ItemProcessResult<T, R>> future = CompletableFuture.supplyAsync(() -> {
                return processItem(item, processor, config);
            }, threadPool);

            futures.add(future);
        }

        // 使用线程安全的队列收集本批次结果
        ConcurrentLinkedQueue<ItemProcessResult<T, R>> batchSuccessResults = new ConcurrentLinkedQueue<>();
        ConcurrentLinkedQueue<ItemProcessResult<T, R>> batchFailureResults = new ConcurrentLinkedQueue<>();
        AtomicInteger batchSuccessCount = new AtomicInteger(0);
        AtomicInteger batchFailureCount = new AtomicInteger(0);

        try {
            // 使用CompletableFuture.allOf()等待所有任务完成，避免顺序阻塞
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );

            // 设置超时等待所有任务完成
            allFutures.get(5, java.util.concurrent.TimeUnit.MINUTES);

            // 收集所有已完成任务的结果（此时不会阻塞，因为所有任务都已完成）
            for (CompletableFuture<ItemProcessResult<T, R>> future : futures) {
                try {
                    ItemProcessResult<T, R> itemResult = future.get();

                    // 无锁添加到本批次结果队列
                    if (itemResult.getSuccess()) {
                        batchSuccessResults.add(itemResult);
                        batchSuccessCount.incrementAndGet();
                    } else {
                        batchFailureResults.add(itemResult);
                        batchFailureCount.incrementAndGet();
                    }

                } catch (Exception e) {
                    log.error("{}：获取单个任务结果失败", config.getBatchName(), e);
                    batchFailureCount.incrementAndGet();
                }
            }

        } catch (java.util.concurrent.TimeoutException e) {
            log.error("{}：批次执行超时（2分钟），开始处理已完成的任务并取消未完成的任务", config.getBatchName(), e);

            // 处理已完成的任务结果，取消未完成的任务
            for (CompletableFuture<ItemProcessResult<T, R>> future : futures) {
                if (future.isDone() && !future.isCancelled()) {
                    try {
                        ItemProcessResult<T, R> itemResult = future.get();
                        if (itemResult.getSuccess()) {
                            batchSuccessResults.add(itemResult);
                            batchSuccessCount.incrementAndGet();
                        } else {
                            batchFailureResults.add(itemResult);
                            batchFailureCount.incrementAndGet();
                        }
                    } catch (Exception ex) {
                        log.error("{}：获取已完成任务结果失败", config.getBatchName(), ex);
                        batchFailureCount.incrementAndGet();
                    }
                } else {
                    // 取消未完成的任务
                    future.cancel(true);
                    batchFailureCount.incrementAndGet();
                }
            }

        } catch (Exception e) {
            log.error("{}：批次处理发生异常", config.getBatchName(), e);

            // 异常情况下，尝试取消所有未完成的任务
            for (CompletableFuture<ItemProcessResult<T, R>> future : futures) {
                if (!future.isDone()) {
                    future.cancel(true);
                }
                batchFailureCount.incrementAndGet();
            }
        }

        // 将本批次结果添加到总结果中
        result.getSuccessResults().addAll(batchSuccessResults);
        result.getFailureResults().addAll(batchFailureResults);

        if (config.getEnableDetailLog()) {
            log.info("{}：第{}批处理完成，成功: {}, 失败: {}, 总计: {}",
                    config.getBatchName(), batchNumber, batchSuccessCount.get(),
                    batchFailureCount.get(), batchItems.size());
        }
    }

    /**
     * 处理单个项目
     */
    private <T, R> ItemProcessResult<T, R> processItem(T item, Function<T, R> processor, BatchProcessConfig config) {
        long startTime = System.currentTimeMillis();

        try {
            // 限流控制：在处理前获取令牌
            RateLimiter rateLimiter = config.getRateLimiter();
            if (rateLimiter != null) {
                double waitTime = rateLimiter.acquire(); // 阻塞直到获得令牌
                if (waitTime > 0 && config.getEnableDetailLog()) {
                    log.debug("{}：限流等待时间: {:.3f}秒", config.getBatchName(), waitTime);
                }
            }

            R result = processor.apply(item);
            long processingTime = System.currentTimeMillis() - startTime;

            // 如果处理结果为null，则认为是失败
            if (result == null) {
                return ItemProcessResult.failure(item, "处理器返回null结果", null, processingTime);
            }

            return ItemProcessResult.success(item, result, processingTime);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            return ItemProcessResult.failure(item, e.getMessage(), e, processingTime);
        }
    }

    /**
     * 创建空结果
     */
    private <T, R> BatchProcessResult<T, R> createEmptyResult() {
        BatchProcessResult<T, R> result = new BatchProcessResult<>();
        result.setTotalCount(0);
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setSuccessRate(0.0);
        result.setSuccessResults(new ArrayList<>());
        result.setFailureResults(new ArrayList<>());
        return result;
    }
}