package com.sankuai.algoplatform.agentapi.infrastructure.entity;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */

import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 知识处理结果类
 */

@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeProcessResult {
    private KnowledgeRecord record;
    private boolean success;
    private String experience;
    private String message;

}