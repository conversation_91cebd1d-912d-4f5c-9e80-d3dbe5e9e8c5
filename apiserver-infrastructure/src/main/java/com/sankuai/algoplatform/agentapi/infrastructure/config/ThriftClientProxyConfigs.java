package com.sankuai.algoplatform.agentapi.infrastructure.config;

import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamQueryThriftService;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamWriteThriftService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */
@Slf4j
@Configuration
public class ThriftClientProxyConfigs {
    @Bean
    public ThriftClientProxy TAgentChatService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.algoplatform.predictor");
        proxy.setServiceInterface(TPredictService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000*6);
        return proxy;
    }

    @Bean
    public ThriftClientProxy IDataStreamWriteThriftService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.cms.knowledge.build");
        proxy.setServiceInterface(IDataStreamWriteThriftService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000);
        return proxy;
    }

    @Bean
    public ThriftClientProxy IDataStreamQueryThriftService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.cms.knowledge.build");
        proxy.setServiceInterface(IDataStreamQueryThriftService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000);
        return proxy;
    }
}
