package com.sankuai.algoplatform.agentapi.infrastructure.factory;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import org.springframework.stereotype.Component;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class ThreadPoolFactory {

    static class CallerRunsPolicy implements RejectedExecutionHandler {
        private String name;

        public CallerRunsPolicy(String name) {
            this.name = name;
        }

        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            if (!e.isShutdown()) {
                r.run();
            }
        }
    }


    private final ThreadPool defaultThreadPool = Rhino.newThreadPool("defaultThreadPool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(50).withMaxSize(128).withMaxQueueSize(200)
                    .withRejectHandler(new CallerRunsPolicy("defaultThreadPool")));


    public ThreadPoolExecutor getDefaultThreadPool() {
        return defaultThreadPool.getExecutor();
    }

    /**
     * 创建自定义线程池
     * 
     * @param name          线程池名称
     * @param corePoolSize  核心线程数
     * @param maxPoolSize   最大线程数
     * @param keepAliveTime 线程存活时间（分钟）
     * @param queueSize     队列大小
     * @return ThreadPoolExecutor实例
     */
    public static ThreadPoolExecutor createThreadPool(String name, int corePoolSize, int maxPoolSize, 
                                                     int keepAliveTime, int queueSize) {
        return Rhino.newThreadPool(name,
                DefaultThreadPoolProperties.Setter()
                        .withCoreSize(corePoolSize)
                        .withMaxSize(maxPoolSize)
                        .withMaxQueueSize(queueSize)
                        .withKeepAliveTimeMinutes(keepAliveTime)
                        .withKeepAliveTimeUnit(TimeUnit.MINUTES)
                        .withRejectHandler(new CallerRunsPolicy(name))).getExecutor();
    }

}
