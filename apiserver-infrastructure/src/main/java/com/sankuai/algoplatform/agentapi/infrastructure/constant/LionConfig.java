package com.sankuai.algoplatform.agentapi.infrastructure.constant;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.algoplatform.agentapi.infrastructure.config.AgentKnowledgeConfig;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/26
 */
@Configuration
public class LionConfig {
    @MdpConfig("continous_learnning_config:{}")
    public static HashMap<String, AgentKnowledgeConfig> AGENT_KNOWLEDGE_MAP;

    @MdpConfig("knowledge_collect_scan_limit:1")
    public static Integer KNOWLEDGE_COLLECT_SCAN_NUM;

    @MdpConfig("knowledge_eval_scan_limit:1")
    public static Integer KNOWLEDGE_EVAL_SCAN_LIMIT;
}
