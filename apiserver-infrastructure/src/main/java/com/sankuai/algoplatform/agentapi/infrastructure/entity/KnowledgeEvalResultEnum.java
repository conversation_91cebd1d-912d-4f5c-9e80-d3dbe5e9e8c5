package com.sankuai.algoplatform.agentapi.infrastructure.entity;

/**
 * 知识评测结果枚举
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/1/8
 */
public enum KnowledgeEvalResultEnum {
    /**
     * 待评测
     */
    PENDING(0, "待评测"),
    
    /**
     * 正向
     */
    POSITIVE(1, "正向"),
    
    /**
     * 负向
     */
    NEGATIVE(2, "负向");

    private final Integer code;
    private final String description;

    KnowledgeEvalResultEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     */
    public static KnowledgeEvalResultEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (KnowledgeEvalResultEnum result : values()) {
            if (result.getCode().equals(code)) {
                return result;
            }
        }
        return null;
    }
}