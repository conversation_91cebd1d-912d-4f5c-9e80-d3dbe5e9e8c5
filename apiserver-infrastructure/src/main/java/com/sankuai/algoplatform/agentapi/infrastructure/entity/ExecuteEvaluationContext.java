package com.sankuai.algoplatform.agentapi.infrastructure.entity;

import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample;
import com.sankuai.algoplatform.agentapi.infrastructure.config.AgentKnowledgeConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExecuteEvaluationContext {

    private KnowledgeEvaluation knowledgeEvaluation;

    private List<KnowledgeLabeledSample> knowledgeLabeledSamples;

    private AgentKnowledgeConfig agentKnowledgeConfig;

    private List<ExecuteEvaluationContextItem> items;

    @Data
    public static class ExecuteEvaluationContextItem {

        private KnowledgeLabeledSample knowledgeLabeledSample;

        private KnowledgeEvaluation knowledgeEvaluation;
    }


}

