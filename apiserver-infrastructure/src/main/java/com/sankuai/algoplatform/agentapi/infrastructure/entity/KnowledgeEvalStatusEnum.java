package com.sankuai.algoplatform.agentapi.infrastructure.entity;

/**
 * 知识评测状态枚举
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/1/8
 */
public enum KnowledgeEvalStatusEnum {
    /**
     * 新创建
     */
    NEW_CREATED(0, "新创建"),
    
    /**
     * 评测中
     */
    EVALUATING(1, "评测中"),
    
    /**
     * 完成评测
     */
    COMPLETED(2, "完成评测"),

    /**
     *  评测失败
     */
    FAILED(-1,"评测失败");

    private final Integer code;
    private final String description;

    KnowledgeEvalStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     */
    public static KnowledgeEvalStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (KnowledgeEvalStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}