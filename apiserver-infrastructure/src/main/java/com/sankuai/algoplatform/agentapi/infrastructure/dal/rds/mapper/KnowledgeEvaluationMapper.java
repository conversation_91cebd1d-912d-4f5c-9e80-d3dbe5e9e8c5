package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.KnowledgeEvaluationExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KnowledgeEvaluationMapper extends MybatisBaseMapper<KnowledgeEvaluation, KnowledgeEvaluationExample, Long> {
    KnowledgeEvaluation selectByPrimaryKey(Long id, String partitionDate);

    int deleteByPrimaryKey(Long id, String partitionDate);

    /**
     * 根据evalStatus查询记录（带limit限制）
     * @param evalStatus 评测状态
     * @param partitionDate 分区日期
     * @param limit 限制条数
     * @return 记录列表
     */
    List<KnowledgeEvaluation> selectByEvalStatusWithLimit(@Param("evalStatus") Integer evalStatus, 
                                                          @Param("partitionDate") String partitionDate, 
                                                          @Param("limit") Integer limit);

    /**
     * 批量更新记录的evalStatus
     * @param ids 需要更新的记录ID列表
     * @param newStatus 新状态
     * @return 更新的记录数量
     */
    int batchUpdateEvalStatus(@Param("ids") List<Long> ids, @Param("newStatus") Integer newStatus);
}