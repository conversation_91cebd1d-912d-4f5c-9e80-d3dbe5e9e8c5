package com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentNodeFlowExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AgentNodeFlowMapper extends MybatisBLOBsMapper<AgentNodeFlow, AgentNodeFlowExample, Long> {
    int batchInsert(@Param("list") List<AgentNodeFlow> list);

    /**
     * 根据session_id, msg_id, current_node_name进行更新式插入
     * @param agentNodeFlow 节点流转对象
     * @return 影响的行数
     */
    int insertOrUpdate(AgentNodeFlow agentNodeFlow);

    /**
     * 批量更新式插入
     * @param list 节点流转对象列表
     * @return 影响的行数
     */
    int batchInsertOrUpdate(@Param("list") List<AgentNodeFlow> list);
}