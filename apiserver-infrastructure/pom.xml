<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.algoplatform.agentapi</groupId>
        <artifactId>apiserver</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>apiserver-infrastructure</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>apiserver-infrastructure</name>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.algoplatform.agentapi</groupId>
            <artifactId>apiserver-client</artifactId>
        </dependency>
		<dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cellar</artifactId>
		</dependency>
		<dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
		</dependency>
		<dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-base</artifactId>
		</dependency>
		<dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
		</dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-crane</artifactId>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
            <artifactId>predictor-client</artifactId>
            <version>1.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83_noneautotype</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.nibhtp.os</groupId>
            <artifactId>htp-cms-knowledge-build-client</artifactId>
            <version>0.0.25</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Guava 依赖，用于 RateLimiter 限流功能 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.1-jre</version>
        </dependency>



</dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
