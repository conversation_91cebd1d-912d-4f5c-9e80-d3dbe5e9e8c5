# 字段说明参见 https://km.sankuai.com/page/13945939
# 注意：如果项目在 Plus 发布系统上使用的是 MDP 发布类型，则项目不使用当前配置。参见：https://km.sankuai.com/custom/onecloud/page/424823177#id-1%E6%A6%82%E8%A7%88
version: v1
common:
build:
    os: centos7
    tools:
        mt_oraclejdk: 8
        maven: 3.9.5
    run:
        workDir: ./
        cmd:
            - sh deploy/compile.sh
    target:
        distDir: ./apiserver-starter/target/
        files:
            - ./*.jar
            - ../../deploy
autodeploy:
    hulkos: centos7
    targetDir: /opt/meituan/com.sankuai.algoplatform.agentapi/
    env:
    run: sh deploy/run.sh
    check: sh deploy/check.sh
    checkRetry: 3 #后面下掉，加默认值为1
    CheckInterval: 20s #后面下掉
    tools:
        mt_oraclejdk: 8
