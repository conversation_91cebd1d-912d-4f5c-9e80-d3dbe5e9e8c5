<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.8.7.6</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.algoplatform.agentapi</groupId>
    <artifactId>apiserver</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>demo</name>

    <modules>
        <module>apiserver-client</module>
        <module>apiserver-starter</module>
        <module>apiserver-application</module>
        <module>apiserver-domain</module>
        <module>apiserver-infrastructure</module>
    </modules>

    <properties>
        <revision>1.0.2</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.algoplatform.agentapi</groupId>
                <artifactId>apiserver-client</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.algoplatform.agentapi</groupId>
                <artifactId>apiserver-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.algoplatform.agentapi</groupId>
                <artifactId>apiserver-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.algoplatform.agentapi</groupId>
                <artifactId>apiserver-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.algoplatform.agentapi</groupId>
                <artifactId>apiserver-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
                <artifactId>open-sdk</artifactId>
                <version>1.0.54-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.it.sso</groupId>
                <artifactId>sso-java-sdk</artifactId>
                <version>2.6.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <profiles>expand</profiles>
                                <dependencies>keep</dependencies>
                                <build>keep</build>
                            </pomElements>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
