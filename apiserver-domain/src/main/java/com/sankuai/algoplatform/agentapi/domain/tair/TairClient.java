package com.sankuai.algoplatform.agentapi.domain.tair;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.taobao.tair3.client.Result;
import com.taobao.tair3.client.ResultMap;
import com.taobao.tair3.client.impl.MultiTairClient;
import com.taobao.tair3.client.util.ByteArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TairClient {
    com.taobao.tair3.client.TairClient.TairOption TAIR_OPTION = new com.taobao.tair3.client.TairClient.TairOption(500, (short) 0, 86400);
    @Resource(name = "sessionTraceStore")
    private MultiTairClient tairClient;

    public Map<String, String> batchGet(String prefix, Collection<String> keys) {
        return batchGet(prefix, keys, "");
    }

    public Map<String, String> batchGet(String prefix, Collection<String> keys, String suffix) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyMap();
        }
        long t0 = System.currentTimeMillis();
        Map<String, String> keyMap = keys.stream().collect(Collectors.toMap(k -> prefix + k + suffix, Function.identity(), (u, v) -> v));

        ResultMap<ByteArray, Result<byte[]>> resp;
        try {
            resp = tairClient.batchGet(getArea(), keyMap.keySet().stream().map(String::getBytes).collect(Collectors.toList()), TAIR_OPTION);
        } catch (Exception e) {
            throw new RuntimeException(String.format("query tair error: %s", keyMap.keySet()), e);
        }

        Map<String, String> res = new HashMap<>(keys.size());
        for (Result<byte[]> r : resp.values()) {
            if (!r.isOK() || r.getKey() == null || r.getResult() == null || r.getResult().length == 0) {
                continue;
            }
            String originKey = keyMap.get(new String(r.getKey()));
            String value = new String(r.getResult());
            res.put(originKey, value);
        }
        log.info("batchGet: prefix:{}, batchSize:{}, suffix:{}, resultSize:{}, cost:{}", prefix, keys.size(), suffix, res.size(), System.currentTimeMillis() - t0);
        return res;
    }

    private short getArea() {
        HostEnv env = MdpContextUtils.getHostEnv();
        if (HostEnv.PROD.equals(env) || HostEnv.STAGING.equals(env)) {
            return 15;
        }
        return 40;
//        return (short) (HostEnv.PROD.equals(MdpContextUtils.getHostEnv()) ? 15 : 40);
    }

    public boolean put(String key, String value, long expireTime, TimeUnit timeUnit) {
        com.taobao.tair3.client.TairClient.TairOption option = new com.taobao.tair3.client.TairClient.TairOption();
        option.setExpireTime((int) timeUnit.toSeconds(expireTime));
        option.setTimeout(500);
        Result<Void> res;
        try {
            res = tairClient.put(getArea(), key.getBytes(), value.getBytes(), option);
            return res.isOK();

        } catch (Exception e) {
            log.error("put error: key:{}, value:{}, ex", key, value, e);
            return false;
        }
    }

    public boolean delete(String key) {
        Result<Void> res;
        try {
            res = tairClient.delete(getArea(), key.getBytes(), null);
            return res.isOK();
        } catch (Exception e) {
            log.error("delete error: key:{}, ex", key, e);
            return false;
        }
    }

    public String get(String key) {
        try {
            Result<byte[]> result = tairClient.get(getArea(), key.getBytes(), TAIR_OPTION);
            if (result.isOK()) {
                return new String(result.getResult());
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("tair#get error", e.getMessage(), e);
            return null;
        }
    }

    public Map<String, Boolean> batchPut(Map<String, String> kvMap, long expireTime, TimeUnit timeUnit) {
        if (MapUtils.isEmpty(kvMap)) {
            return Collections.emptyMap();
        }
        com.taobao.tair3.client.TairClient.TairOption option = new com.taobao.tair3.client.TairClient.TairOption();
        option.setExpireTime((int) timeUnit.toSeconds(expireTime));
        option.setTimeout(500);
        Map<ByteArray, byte[]> kv = new HashMap<>(kvMap.size());
        try {
            kvMap.forEach((k,v) -> {
                if (k == null || v == null) {
                    return;
                }
                ByteArray originKey = new ByteArray(k.getBytes());
                byte[] originVal = v.getBytes();
                kv.put(originKey, originVal);
            });
            ResultMap<ByteArray, Result<Void>> resultMap = tairClient.batchPut(getArea(), kv, option);
            Map<String, Boolean> res = new HashMap<>();
            if (resultMap.isOK()) {
                resultMap.forEach((originKey, result) -> {
                    String key = new String(originKey.getBytes());
                    res.put(key, result.isOK());
                });
                return res;
            }
            log.warn("batchPut fail: kvMap:{}, resultMap:{}", kvMap, resultMap);
            return Collections.emptyMap();
        } catch (Exception e) {
            log.error("batchPut error: kvMap:{}, ex", kvMap, e);
            return Collections.emptyMap();
        }
    }

    /**
     * Session连接维护相关接口
     */
    private static final String SESSION_IP_PREFIX = "session_ip_mapping:";
    private static final long SESSION_IP_EXPIRE_TIME = 24; // 24小时过期

    /**
     * 获取Session对应的IP映射关系
     *
     * @param sessionId 会话ID
     * @return IP地址，如果没有映射关系则返回null
     */
    public String getSessionIpMapping(String sessionId) {
        if (sessionId == null || sessionId.isEmpty()) {
            return null;
        }
        String key = SESSION_IP_PREFIX + sessionId;
        try {
            String ip = get(key);
            log.debug("获取Session IP映射: sessionId={}, ip={}", sessionId, ip);
            return ip;
        } catch (Exception e) {
            log.error("获取Session IP映射失败: sessionId={}", sessionId, e);
            return null;
        }
    }

    /**
     * 设置Session对应的IP映射关系
     *
     * @param sessionId 会话ID
     * @param ip IP地址
     * @return 设置是否成功
     */
    public boolean setSessionIpMapping(String sessionId, String ip) {
        if (sessionId == null || sessionId.isEmpty() || ip == null || ip.isEmpty()) {
            return false;
        }
        String key = SESSION_IP_PREFIX + sessionId;
        try {
            boolean result = put(key, ip, SESSION_IP_EXPIRE_TIME, TimeUnit.HOURS);
            log.debug("设置Session IP映射: sessionId={}, ip={}, result={}", sessionId, ip, result);
            return result;
        } catch (Exception e) {
            log.error("设置Session IP映射失败: sessionId={}, ip={}", sessionId, ip, e);
            return false;
        }
    }

    /**
     * 删除Session对应的IP映射关系
     *
     * @param sessionId 会话ID
     * @return 删除是否成功
     */
    public boolean removeSessionIpMapping(String sessionId) {
        if (sessionId == null || sessionId.isEmpty()) {
            return false;
        }
        String key = SESSION_IP_PREFIX + sessionId;
        try {
            // 通过设置过期时间为0来删除key
            boolean result = delete(key);
            log.debug("删除Session IP映射: sessionId={}, result={}", sessionId, result);
            return result;
        } catch (Exception e) {
            log.error("删除Session IP映射失败: sessionId={}", sessionId, e);
            return false;
        }
    }
}
