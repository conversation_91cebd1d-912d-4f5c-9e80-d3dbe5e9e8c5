package com.sankuai.algoplatform.agentapi.domain.s3;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.File;

/**
 * <AUTHOR>
 * @date 2024年03月27日 6:08 下午
 */

@Slf4j
@Service
public class AmazonS3Service {

    public void putObjectExample(AmazonS3 s3Client, String bucketName, String objectName, String content){
        try{
            //bucketName指定上传文件所在的桶名（服务秘钥要用桶别名）
            //objectName指定上传的文件名
            //content指定上传的文件内容
            s3Client.putObject(bucketName,objectName,new ByteArrayInputStream(content.getBytes()),null);
        }catch (AmazonServiceException ase) {
            //存储服务端处理异常
            Cat.logError(ase);
        } catch (AmazonClientException ace) {
            //客户端处理异常
            Cat.logError(ace);
        }
    }

    public void getObject(AmazonS3 s3Client, String bucketName, String objectName , File file){
        try {
            s3Client.getObject(new GetObjectRequest(bucketName, objectName), file);
        } catch (AmazonClientException e) {
            Cat.logError(e);
        }
    }

    public File getFile(AmazonS3 s3Client, String bucketName, String objectName) {
        if (objectName == null) {
            return null;
        }
        File file = new File(objectName);
        getObject(s3Client, bucketName, objectName, file);
        return file;
    }
}
