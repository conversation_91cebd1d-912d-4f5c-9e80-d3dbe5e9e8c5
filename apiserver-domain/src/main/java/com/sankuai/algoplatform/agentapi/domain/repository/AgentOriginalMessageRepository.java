package com.sankuai.algoplatform.agentapi.domain.repository;

import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessage;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs;

import java.util.List;

/**
 * AgentOriginalMessage 数据访问对象接口
 */
public interface AgentOriginalMessageRepository {
    
    /**
     * 根据消息ID查询原始消息
     * 
     * @param msgId 消息ID
     * @return 原始消息对象，如果不存在则返回null
     */
    AgentOriginalMessage getByMsgId(String msgId);
    
    /**
     * 根据会话ID查询原始消息列表
     * 
     * @param sessionId 会话ID
     * @return 原始消息列表
     */
    List<AgentOriginalMessage> listBySessionId(String sessionId);
    
    /**
     * 根据会话ID和消息来源查询原始消息列表
     * 
     * @param sessionId 会话ID
     * @param source 消息来源
     * @return 原始消息列表
     */
    List<AgentOriginalMessage> listBySessionIdAndSource(String sessionId, String source);
    
    /**
     * 根据会话ID和消息类型查询原始消息列表
     * 
     * @param sessionId 会话ID
     * @param type 消息类型
     * @return 原始消息列表
     */
    List<AgentOriginalMessage> listBySessionIdAndType(String sessionId, String type);
    
    /**
     * 插入新原始消息
     * 
     * @param agentOriginalMessage 原始消息对象
     * @return 影响的行数
     */
    int insert(AgentOriginalMessageWithBLOBs agentOriginalMessage);

    /**
     * 插入新原始消息
     *
     * @param agentOriginalMessages 原始消息对象
     * @return 影响的行数
     */
    int batchInsert(List<AgentOriginalMessageWithBLOBs> agentOriginalMessages);

    /**
     * 根据msg_id进行更新式插入
     *
     * @param agentOriginalMessage 原始消息对象
     * @return 影响的行数
     */
    int insertOrUpdate(AgentOriginalMessageWithBLOBs agentOriginalMessage);

    /**
     * 批量更新式插入
     *
     * @param agentOriginalMessages 原始消息对象列表
     * @return 影响的行数
     */
    int batchInsertOrUpdate(List<AgentOriginalMessageWithBLOBs> agentOriginalMessages);

    /**
     * 更新原始消息
     *
     * @param agentOriginalMessage 原始消息对象
     * @return 影响的行数
     */
    int update(AgentOriginalMessage agentOriginalMessage);
    
    /**
     * 根据消息ID删除原始消息
     * 
     * @param msgId 消息ID
     * @return 影响的行数
     */
    int deleteByMsgId(String msgId);
    
    /**
     * 根据会话ID删除所有原始消息
     * 
     * @param sessionId 会话ID
     * @return 影响的行数
     */
    int deleteBySessionId(String sessionId);
    
    /**
     * 分页查询会话的原始消息列表
     * 
     * @param sessionId 会话ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 原始消息列表
     */
    List<AgentOriginalMessage> pageQueryBySessionId(String sessionId, int offset, int limit);
    
    /**
     * 计算会话的原始消息总数
     * 
     * @param sessionId 会话ID
     * @return 原始消息总数
     */
    long countBySessionId(String sessionId);
}