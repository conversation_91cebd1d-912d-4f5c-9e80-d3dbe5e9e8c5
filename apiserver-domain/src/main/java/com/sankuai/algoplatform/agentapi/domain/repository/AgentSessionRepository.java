package com.sankuai.algoplatform.agentapi.domain.repository;

import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession;

import java.util.List;

/**
 * AgentSession数据访问对象接口
 */
public interface AgentSessionRepository {
    
    /**
     * 根据会话ID查询会话
     * 
     * @param sessionId 会话ID
     * @return 会话对象，如果不存在则返回null
     */
    AgentSession getBySessionId(String sessionId);
    
    /**
     * 根据用户查询会话列表
     * 
     * @param owner 用户名
     * @return 会话列表
     */
    List<AgentSession> listByOwner(String owner);
    
    /**
     * 根据Agent代码查询会话列表
     * 
     * @param agentCode Agent代码
     * @return 会话列表
     */
    List<AgentSession> listByAgentCode(String agentCode);
    
    /**
     * 根据状态查询会话列表
     * 
     * @param status 状态码
     * @return 会话列表
     */
    List<AgentSession> listByStatus(Integer status);
    
    /**
     * 插入新会话
     * 
     * @param agentSession 会话对象
     * @return 影响的行数
     */
    int insert(AgentSession agentSession);
    
    /**
     * 更新会话
     * 
     * @param agentSession 会话对象
     * @return 影响的行数
     */
    int update(AgentSession agentSession);
    
    /**
     * 根据会话ID删除会话
     * 
     * @param sessionId 会话ID
     * @return 影响的行数
     */
    int deleteBySessionId(String sessionId);
    
    /**
     * 分页查询会话列表
     * 
     * @param owner 用户名，可为null
     * @param agentCode Agent代码，可为null
     * @param status 状态码，可为null
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 会话列表
     */
    List<AgentSession> pageQuery(String owner, String agentCode, Integer status, int offset, int limit);
    
    /**
     * 计算符合条件的会话总数
     * 
     * @param owner 用户名，可为null
     * @param agentCode Agent代码，可为null
     * @param status 状态码，可为null
     * @return 会话总数
     */
    long countQuery(String owner, String agentCode, Integer status);
}