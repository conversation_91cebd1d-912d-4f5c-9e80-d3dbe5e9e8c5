package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.amazonaws.util.IOUtils;
import com.dianping.cat.Cat;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.agentapi.client.dto.AgentStateDTO;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.state.TStateResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentStateService;
import com.sankuai.algoplatform.agentapi.domain.repository.AgentStateRepository;
import com.sankuai.algoplatform.agentapi.domain.s3.AmazonS3ClientUtil;
import com.sankuai.algoplatform.agentapi.domain.s3.AmazonS3Service;
import com.sankuai.algoplatform.agentapi.domain.utils.Md5Utils;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * AgentStateService 接口实现类
 */
@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TAgentStateServiceImpl implements TAgentStateService {

    @Autowired
    private AgentStateRepository agentStateRepository;

    @Autowired
    private AmazonS3Service amazonS3Service;

    @Override
    public TStateResponse getAgentState(String sessionId) {
        try {
            AgentState state = agentStateRepository.getLatestBySessionId(sessionId);
            AgentStateDTO dto = state != null ? convertToDTO(state) : null;
            if (dto == null) {
                return null;
            }
            try {
                String fileName = dto.getTeamState();
                File file = amazonS3Service.getFile(AmazonS3ClientUtil.getBmlS3Client(), "dzdml-match", fileName);
                InputStream inputStream = new FileInputStream(file);
                String content = IOUtils.toString(inputStream);
                dto.setTeamState(content);
            } catch (Exception e) {
                log.error("文件消息获取错误",e);
                return null;
            }
            return TStateResponse.success(dto);
        } catch (Exception e) {
            log.error("获取Agent状态失败: ",e);
            return TStateResponse.fail("获取Agent状态失败: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse saveAgentState(AgentStateDTO agentStateDTO) {
        try {
            AgentState state = convertToModel(agentStateDTO);
            state.setStatus(0);
            String md5Str = Md5Utils.generateMd5(state.getTeamState()) + System.currentTimeMillis();
            amazonS3Service.putObjectExample(AmazonS3ClientUtil.getBmlS3Client(), "dzdml-match", md5Str, state.getTeamState());
            state.setTeamState(md5Str);
            agentStateRepository.insert(state);
            return TCheckResponse.success(true);
        } catch (Exception e) {
            log.error("保存Agent状态失败: ",e);
            return TCheckResponse.fail("保存Agent状态失败: " + e.getMessage());
        }
    }

    /**
     * 将领域模型转换为DTO
     *
     * @param state 领域模型
     * @return DTO
     */
    private AgentStateDTO convertToDTO(AgentState state) {
        if (state == null) {
            return null;
        }
        AgentStateDTO dto = new AgentStateDTO();
        BeanUtils.copyProperties(state, dto);
        return dto;
    }

    /**
     * 将DTO转换为领域模型
     *
     * @param dto DTO
     * @return 领域模型
     */
    private AgentState convertToModel(AgentStateDTO dto) {
        if (dto == null) {
            return null;
        }
        AgentState model = new AgentState();
        BeanUtils.copyProperties(dto, model);
        return model;
    }
}