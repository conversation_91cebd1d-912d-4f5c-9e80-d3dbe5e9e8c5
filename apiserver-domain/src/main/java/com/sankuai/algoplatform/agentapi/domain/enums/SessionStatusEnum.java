package com.sankuai.algoplatform.agentapi.domain.enums;

/**
 * <AUTHOR>
 * @date 2025年05月26日 11:25
 */
public enum SessionStatusEnum {
    PRE_ALLOCATE(-2, "预分配"),
    DELETED(-1, "删除"),
    RUNNING(0, "执行中"),
    FINISHED(1, "执行完成"),
    ERROR(2, "执行出错"),
    PENDING(3, "暂停中"),
    WAITCHAT(4, "等待会话输入");

    SessionStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态码获取状态描述
     * @param code 状态码
     * @return 状态描述，如果找不到则返回"未知状态(code)"
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "空状态";
        }

        for (SessionStatusEnum status : SessionStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }

        return "未知状态(" + code + ")";
    }
}
