package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.agentapi.client.request.SendGroupCardRequest;
import com.sankuai.algoplatform.agentapi.client.request.SendMisCardRequest;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import com.sankuai.algoplatform.agentapi.client.response.TStringResponse;
import com.sankuai.algoplatform.agentapi.client.service.TOpenCardService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.QueryEmpIdentityByMisListReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.QueryEmpIdentityByMisListResp;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.UserIdentity;
import com.sankuai.sso.oidc.client.OAuthClient;
import com.sankuai.sso.oidc.client.OAuthClientConfig;
import com.sankuai.sso.oidc.client.OAuthClientConfigBuilder;
import com.sankuai.sso.oidc.client.OAuthClientFactory;
import com.sankuai.sso.oidc.enums.GrantTypeEnum;
import com.sankuai.sso.oidc.enums.RequestTokenResultEnum;
import com.sankuai.sso.oidc.request.OAuthTokenRequest;
import com.sankuai.sso.oidc.request.OAuthTokenRequestBuilder;
import com.sankuai.sso.oidc.response.OAuthTokenResponse;
import com.sankuai.xm.openplatform.api.entity.*;
import com.sankuai.xm.openplatform.api.service.open.OpenCardServiceI;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Agent聊天服务实现
 *
 * <AUTHOR>
 * @date 2025年05月28日
 */
@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TOpenCardServiceImpl implements TOpenCardService {
    @Value("${appId:6ee161f17b}")
    private String appId;
    @Value("${appSecret:5a09dc53c627446689e0b7ccb6cc3773}")
    private String appSecret;

    @Autowired
    private DxService dxService;
    @Autowired
    private XmAuthServiceI.Iface xmAuthService;
    @Autowired
    private OpenCardServiceI.Iface openCardService;

    @Override
    public TBaseReponse sendMisCardWithoutSessionId(Long templateId, List<String> misList, String abstractText, Map<String, String> variableValue) {
        // 构造请求对象
        SendMisCardRequest request = new SendMisCardRequest();
        request.setTemplateId(templateId);
        request.setMisList(misList);
        request.setAbstractText(abstractText);
        request.setSessionId(null);
        request.setVariableValue(variableValue);

        return sendMisCard(request);
    }

    @Override
    public TBaseReponse sendGroupCard(SendGroupCardRequest request) {
        String requestId = String.valueOf(System.currentTimeMillis());
        String accessToken = getAccessToken(appId, appSecret);
        SendSharingCardReq sendSharingCardReq = new SendSharingCardReq();
        sendSharingCardReq.setRequestId(requestId);
        sendSharingCardReq.setSerialNum(requestId);
        sendSharingCardReq.setTemplateId(request.getTemplateId());
        if (request.getSessionId() != null) {
            request.getVariableValue().put("sessionId", request.getSessionId());
        }
        sendSharingCardReq.setVariableValue(JsonUtils.toJson(request.getVariableValue()));
        sendSharingCardReq.setAbstractText(request.getAbstractText());
        sendSharingCardReq.setVersion(System.currentTimeMillis());
        sendSharingCardReq.setFieldTypes(Sets.newHashSet(CardFieldTypeEnum.GROUP_CHAT));
        SharingCardFieldData sharingCardFieldData = new SharingCardFieldData();
        SharingIMGroupChat sharingIMGroupChat = new SharingIMGroupChat();
        sharingIMGroupChat.setGid(request.getGid());
        sharingCardFieldData.setImGroupChat(sharingIMGroupChat);
        sendSharingCardReq.setCardFieldData(sharingCardFieldData);
        try {
            SendSharingCardResp sendSharingCardResp = openCardService.sendSharingCard(accessToken, sendSharingCardReq);
            log.info("发送群聊卡片消息,req={},resp={}", JSON.toJSONString(sendSharingCardReq), JSON.toJSONString(sendSharingCardResp));
            TStringResponse response = new TStringResponse();
            response.setCode(200);
            response.setMessage("success");
            return response;
        } catch (Exception e) {
            log.error("发送群聊卡片消息异常,req={}", JSON.toJSONString(sendSharingCardReq), e);
            TStringResponse response = new TStringResponse();
            response.setCode(500);
            response.setMessage("failed");
            response.setData("发送群聊卡片消息异常: " + e.getMessage());
            return response;
        }
    }

    @Override
    public TBaseReponse sendMisCard(SendMisCardRequest request) {
        List<Long> empIdByMisId = getEmpIdByMis(request.getMisList());
        String requestId = String.valueOf(System.currentTimeMillis());
        String accessToken = getAccessToken(appId, appSecret);
        SendSharingCardReq sendSharingCardReq = new SendSharingCardReq();
        sendSharingCardReq.setRequestId(requestId);
        sendSharingCardReq.setSerialNum(requestId);
        sendSharingCardReq.setTemplateId(request.getTemplateId());
        if (request.getSessionId() != null) {
            request.getVariableValue().put("sessionId", request.getSessionId());
        }
        sendSharingCardReq.setVariableValue(JsonUtils.toJson(request.getVariableValue()));
        sendSharingCardReq.setAbstractText(request.getAbstractText());
        sendSharingCardReq.setVersion(System.currentTimeMillis());
        sendSharingCardReq.setFieldTypes(Sets.newHashSet(CardFieldTypeEnum.PUB_CHAT));
        SharingCardFieldData sharingCardFieldData = new SharingCardFieldData();
        SharingIMBotChat sharingIMBotChat = new SharingIMBotChat();
        sharingIMBotChat.setEmpIds(new HashSet<>(empIdByMisId));
        sharingCardFieldData.setImBotChat(sharingIMBotChat);
        sendSharingCardReq.setCardFieldData(sharingCardFieldData);
        try {
            SendSharingCardResp sendSharingCardResp = openCardService.sendSharingCard(accessToken, sendSharingCardReq);
            log.info("发送单聊卡片消息,req={},resp={}", JSON.toJSONString(sendSharingCardReq), JSON.toJSONString(sendSharingCardResp));
            TStringResponse response = new TStringResponse();
            response.setCode(200);
            response.setMessage("success");
            return response;
        } catch (Exception e) {
            log.error("发送单聊卡片消息异常,req={}", JSON.toJSONString(sendSharingCardReq), e);
            TStringResponse response = new TStringResponse();
            response.setCode(500);
            response.setMessage("failed");
            response.setData("发送单聊卡片消息异常: " + e.getMessage());
            return response;
        }
    }

    private String getAccessToken(String appId, String appSecret) {
        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppkey(appId);
        appAuthInfo.setAppSecret(appSecret);

        try {
            AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
            log.info("获取accessToken,resp={}", JSON.toJSONString(resp));
            if (resp.status.getCode() == 0) {
                return resp.getAccessToken().getToken();
            } else {
                throw new Exception("获取accessToken失败");
            }
        } catch (Exception e) {
            log.error("获取accessToken异常,appAuthInfo={}", JSON.toJSONString(appAuthInfo), e);
            return null;
        }
    }

    public List<Long> getEmpIdByMis(List<String> misList) {
        String accessToken = getQpToken(appId, appSecret, "xm-xai");//请求的是大象接口，所以这里写大象的audience
        QueryEmpIdentityByMisListReq req = new QueryEmpIdentityByMisListReq();
        req.setMisList(misList);
        try {
            QueryEmpIdentityByMisListResp resp = dxService.queryEmpIdentityByMisList(accessToken, req);
            log.info("根据misList获取empId,req={},resp={}", JSON.toJSONString(req), JSON.toJSONString(resp));
            if (ResCodeEnum.SUCCESS.getCode() == resp.getStatus().getCode()) {
                Map<String, UserIdentity> data = resp.getData().getData();
                List<Long> empIdList = misList.stream().map(e -> data.get(e).getEmpId()).collect(Collectors.toList());
                return empIdList;
            }
        } catch (Exception e) {
            log.error("根据misList获取empId失败,e={},req={}", e, misList);
        }
        return Lists.newArrayList();
    }

    //详细使用见 https://km.sankuai.com/collabpage/2709674507
    public String getQpToken(String testAppId, String testAppSecret, String audience) {
        OAuthClientFactory.initialize();
        OAuthClientConfig config = OAuthClientConfigBuilder.create()
                .clientId(testAppId) // 必填，应用自身的 clientId
                .clientSecret(testAppSecret) // 必填，应用自身的 clientSecret
                .build();

        OAuthClient oAuthClient = OAuthClientFactory.createClient(config); //建议创建成全局唯一实例，每个实例会有内存占用消耗，尽量复用而不是创建多个
        OAuthTokenRequest request = OAuthTokenRequestBuilder.create()
                .grantType(GrantTypeEnum.ClientCredentials)
                .audience(new String[]{audience}) //可选，如果添加了 audience，返回的票据里会自动把该 clientIdA 加入到 audience 里，相当于省去一次换票动作。
                .build();

        OAuthTokenResponse response = oAuthClient.requestToken(request);
        RequestTokenResultEnum tokenResult = response.getRequestTokenResult();
        if (tokenResult != RequestTokenResultEnum.SUCCESS) {
            throw new RuntimeException("生成应用平台Token失败");
        }
        return response.getAccessToken();
    }
}
