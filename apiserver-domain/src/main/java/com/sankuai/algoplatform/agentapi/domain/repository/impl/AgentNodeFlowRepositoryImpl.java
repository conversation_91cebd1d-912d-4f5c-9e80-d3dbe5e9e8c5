package com.sankuai.algoplatform.agentapi.domain.repository.impl;

import com.sankuai.algoplatform.agentapi.domain.repository.AgentNodeFlowRepository;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentNodeFlowExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentNodeFlowMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * AgentNodeFlow 接口实现类
 *
 * <AUTHOR>
 * @date 2025年06月03日 19:19
 */
@Repository
public class AgentNodeFlowRepositoryImpl implements AgentNodeFlowRepository {

    @Resource
    private AgentNodeFlowMapper agentNodeFlowMapper;

    // 状态常量
    private static final Integer STATUS_NORMAL = 0;
    private static final Integer STATUS_DELETED = -1;

    @Override
    public AgentNodeFlow getById(Long id) {
        return agentNodeFlowMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AgentNodeFlow> listBySessionId(String sessionId) {
        return listBySessionId(sessionId, false);
    }

    @Override
    public List<AgentNodeFlow> listBySessionId(String sessionId, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria().andSessionIdEqualTo(sessionId);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        example.setOrderByClause("add_time asc");
        return agentNodeFlowMapper.selectByExample(example);
    }

    @Override
    public List<AgentNodeFlow> listByMsgId(String msgId) {
        return listByMsgId(msgId, false);
    }

    @Override
    public List<AgentNodeFlow> listByMsgId(String msgId, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria().andMsgIdEqualTo(msgId);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        example.setOrderByClause("add_time DESC");
        return agentNodeFlowMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentNodeFlow> listBySessionIdAndMsgId(String sessionId, String msgId) {
        return listBySessionIdAndMsgId(sessionId, msgId, false);
    }

    @Override
    public List<AgentNodeFlow> listBySessionIdAndMsgId(String sessionId, String msgId, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andMsgIdEqualTo(msgId);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        example.setOrderByClause("add_time DESC");
        return agentNodeFlowMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentNodeFlow> listByCurrentNodeName(String currentNodeName) {
        return listByCurrentNodeName(currentNodeName, false);
    }

    @Override
    public List<AgentNodeFlow> listByCurrentNodeName(String currentNodeName, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria().andCurrentNodeNameEqualTo(currentNodeName);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        example.setOrderByClause("add_time ASC");
        return agentNodeFlowMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentNodeFlow> listBySessionIdAndCurrentNodeName(String sessionId, String currentNodeName) {
        return listBySessionIdAndCurrentNodeName(sessionId, currentNodeName, false);
    }

    @Override
    public List<AgentNodeFlow> listBySessionIdAndCurrentNodeName(String sessionId, String currentNodeName, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andCurrentNodeNameEqualTo(currentNodeName);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        example.setOrderByClause("add_time ASC");
        return agentNodeFlowMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentNodeFlow> listByNextNodeName(String nextNodeName) {
        return listByNextNodeName(nextNodeName, false);
    }

    @Override
    public List<AgentNodeFlow> listByNextNodeName(String nextNodeName, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria().andNextNodeNameEqualTo(nextNodeName);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        example.setOrderByClause("add_time DESC");
        return agentNodeFlowMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentNodeFlow> listByCurrentNodeStatus(String currentNodeStatus) {
        return listByCurrentNodeStatus(currentNodeStatus, false);
    }

    @Override
    public List<AgentNodeFlow> listByCurrentNodeStatus(String currentNodeStatus, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria().andCurrentNodeStatusEqualTo(currentNodeStatus);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        example.setOrderByClause("add_time DESC");
        return agentNodeFlowMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public int insert(AgentNodeFlow agentNodeFlow) {
        // 设置默认状态为正常
        if (agentNodeFlow.getStatus() == null) {
            agentNodeFlow.setStatus(STATUS_NORMAL);
        }
        // 设置默认添加时间
        if (agentNodeFlow.getAddTime() == null) {
            agentNodeFlow.setAddTime(new Date());
        }
        return agentNodeFlowMapper.insert(agentNodeFlow);
    }

    @Override
    public int batchInsert(List<AgentNodeFlow> agentNodeFlows) {
        // 设置默认状态和添加时间
        Date now = new Date();
        for (AgentNodeFlow flow : agentNodeFlows) {
            if (flow.getStatus() == null) {
                flow.setStatus(STATUS_NORMAL);
            }
            if (flow.getAddTime() == null) {
                flow.setAddTime(now);
            }
        }
        return agentNodeFlowMapper.batchInsert(agentNodeFlows);
    }

    @Override
    public int insertOrUpdate(AgentNodeFlow agentNodeFlow) {
        // 设置默认状态为正常
        if (agentNodeFlow.getStatus() == null) {
            agentNodeFlow.setStatus(STATUS_NORMAL);
        }
        // 设置默认添加时间
        if (agentNodeFlow.getAddTime() == null) {
            agentNodeFlow.setAddTime(new Date());
        }
        return agentNodeFlowMapper.insertOrUpdate(agentNodeFlow);
    }

    @Override
    public int batchInsertOrUpdate(List<AgentNodeFlow> agentNodeFlows) {
        // 设置默认状态和添加时间
        Date now = new Date();
        for (AgentNodeFlow flow : agentNodeFlows) {
            if (flow.getStatus() == null) {
                flow.setStatus(STATUS_NORMAL);
            }
            if (flow.getAddTime() == null) {
                flow.setAddTime(now);
            }
        }
        return agentNodeFlowMapper.batchInsertOrUpdate(agentNodeFlows);
    }

    @Override
    public int update(AgentNodeFlow agentNodeFlow) {
        return agentNodeFlowMapper.updateByPrimaryKeySelective(agentNodeFlow);
    }

    @Override
    public int deleteById(Long id) {
        return agentNodeFlowMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int logicDeleteById(Long id) {
        AgentNodeFlow agentNodeFlow = new AgentNodeFlow();
        agentNodeFlow.setId(id);
        agentNodeFlow.setStatus(STATUS_DELETED);
        return agentNodeFlowMapper.updateByPrimaryKeySelective(agentNodeFlow);
    }

    @Override
    public int deleteBySessionId(String sessionId) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        return agentNodeFlowMapper.deleteByExample(example);
    }

    @Override
    public int logicDeleteBySessionId(String sessionId) {
        AgentNodeFlow agentNodeFlow = new AgentNodeFlow();
        agentNodeFlow.setStatus(STATUS_DELETED);

        AgentNodeFlowExample example = new AgentNodeFlowExample();
        example.createCriteria().andSessionIdEqualTo(sessionId).andStatusEqualTo(STATUS_NORMAL);

        return agentNodeFlowMapper.updateByExampleSelective(agentNodeFlow, example);
    }

    @Override
    public int deleteByMsgId(String msgId) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        example.createCriteria().andMsgIdEqualTo(msgId);
        return agentNodeFlowMapper.deleteByExample(example);
    }

    @Override
    public int logicDeleteByMsgId(String msgId) {
        AgentNodeFlow agentNodeFlow = new AgentNodeFlow();
        agentNodeFlow.setStatus(STATUS_DELETED);

        AgentNodeFlowExample example = new AgentNodeFlowExample();
        example.createCriteria().andMsgIdEqualTo(msgId).andStatusEqualTo(STATUS_NORMAL);

        return agentNodeFlowMapper.updateByExampleSelective(agentNodeFlow, example);
    }

    @Override
    public List<AgentNodeFlow> pageQueryBySessionId(String sessionId, int offset, int limit) {
        return pageQueryBySessionId(sessionId, offset, limit, false);
    }

    @Override
    public List<AgentNodeFlow> pageQueryBySessionId(String sessionId, int offset, int limit, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria().andSessionIdEqualTo(sessionId);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        example.setOrderByClause("add_time DESC");
        example.setOffset(offset);
        example.setRows(limit);
        return agentNodeFlowMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public long countBySessionId(String sessionId) {
        return countBySessionId(sessionId, false);
    }

    @Override
    public long countBySessionId(String sessionId, boolean includeDeleted) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        AgentNodeFlowExample.Criteria criteria = example.createCriteria().andSessionIdEqualTo(sessionId);
        if (!includeDeleted) {
            criteria.andStatusEqualTo(STATUS_NORMAL);
        }
        return agentNodeFlowMapper.countByExample(example);
    }

    @Override
    public List<AgentNodeFlow> listBySessionIdAndCurrentNodeNameDESC(String sessionId, String currentNodeName) {
        AgentNodeFlowExample example = new AgentNodeFlowExample();
        example.createCriteria().andSessionIdEqualTo(sessionId).andCurrentNodeNameEqualTo(currentNodeName).andStatusEqualTo(STATUS_NORMAL);
        example.setOrderByClause("add_time DESC");
        return agentNodeFlowMapper.selectByExample(example);
    }
}