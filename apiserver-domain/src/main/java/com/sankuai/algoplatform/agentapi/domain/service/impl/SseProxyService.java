package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ScheduledThreadPool;
import com.dianping.rhino.threadpool.ThreadPool;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.dto.AgentNodeFlowDTO;
import com.sankuai.algoplatform.agentapi.client.dto.AgentOriginalMessageDTO;
import com.sankuai.algoplatform.agentapi.client.enums.ResponseCodeEnum;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.TStringResponse;
import com.sankuai.algoplatform.agentapi.client.response.session.TSessionResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentMessageService;
import com.sankuai.algoplatform.agentapi.client.service.TAgentNodeFlowService;
import com.sankuai.algoplatform.agentapi.client.service.TAgentSessionService;
import com.sankuai.algoplatform.agentapi.infrastructure.constant.CommonConstants;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static com.sankuai.algoplatform.agentapi.domain.enums.SessionStatusEnum.FINISHED;
import static com.sankuai.algoplatform.agentapi.domain.enums.SessionStatusEnum.ERROR;
import static com.sankuai.algoplatform.agentapi.domain.enums.SessionStatusEnum.WAITCHAT;
import static com.sankuai.algoplatform.agentapi.domain.enums.SessionStatusEnum.PENDING;

@Service
@Slf4j
public class SseProxyService {
    private static ThreadPool backendSSEPool = Rhino.newThreadPool("BackendSSEPool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(128)
                    .withMaxSize(128)
                    .withMaxQueueSize(128)
    );
    private static ScheduledThreadPool heartBeatPool = Rhino.newScheduledThreadPool("HeartBeatPool",
            DefaultThreadPoolProperties.Setter().
                    withCoreSize(128)
                    .withMaxSize(128)
                    .withMaxQueueSize(128)
    );

    // 存储基于sessionId的SSE连接池，每个sessionId对应一个连接集合
    private final Map<String, Set<SseEmitter>> sessionEmitterPools = new ConcurrentHashMap<>();

    // 存储每个emitter对应的连接信息，用于清理时查找
    private final Map<SseEmitter, ConnectionInfo> emitterConnectionMap = new ConcurrentHashMap<>();

    // 存储心跳检测任务，用于取消任务
    private final Map<String, ScheduledFuture<?>> heartbeatTasks = new ConcurrentHashMap<>();

    // 存储到Python后端的连接状态，用于去重
    private final Map<String, BackendConnectionInfo> backendConnections = new ConcurrentHashMap<>();

    // 存储session与start_message的映射关系，用于复用连接时推送
    private final Map<String, StartMessageInfo> sessionStartMessages = new ConcurrentHashMap<>();

    // 连接信息内部类
    private static class ConnectionInfo {
        private final String sessionId;
        private final String misId;

        public ConnectionInfo(String sessionId, String misId) {
            this.sessionId = sessionId;
            this.misId = misId;
        }

        public String getSessionId() {
            return sessionId;
        }

        public String getMisId() {
            return misId;
        }
    }

    // Python后端连接信息内部类
    private static class BackendConnectionInfo {
        private final String sessionId;
        private final String misId;
        private final String agentCode;
        private final long createTime;
        private volatile boolean isActive;

        public BackendConnectionInfo(String sessionId, String misId, String agentCode) {
            this.sessionId = sessionId;
            this.misId = misId;
            this.agentCode = agentCode;
            this.createTime = System.currentTimeMillis();
            this.isActive = true;
        }

        public String getSessionId() {
            return sessionId;
        }

        public String getMisId() {
            return misId;
        }

        public String getAgentCode() {
            return agentCode;
        }

        public long getCreateTime() {
            return createTime;
        }

        public boolean isActive() {
            return isActive;
        }

        public void setActive(boolean active) {
            isActive = active;
        }
    }

    // Start Message信息内部类
    private static class StartMessageInfo {
        private final String sessionId;
        private final String eventName;
        private final String eventId;
        private final String data;
        private final long createTime;

        public StartMessageInfo(String sessionId, String eventName, String eventId, String data) {
            this.sessionId = sessionId;
            this.eventName = eventName;
            this.eventId = eventId;
            this.data = data;
            this.createTime = System.currentTimeMillis();
        }

        public String getSessionId() {
            return sessionId;
        }

        public String getEventName() {
            return eventName;
        }

        public String getEventId() {
            return eventId;
        }

        public String getData() {
            return data;
        }

        public long getCreateTime() {
            return createTime;
        }
    }

    @Value("${backend.sse.url:http://127.0.0.1:8000/api/sse}")
    private String backendSseUrl;

    @Value("${backend.connections.check.url:http://127.0.0.1:8000/api/connections/check-and-refresh}")
    private String backendConnectionsCheckUrl;

    private final RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private TAgentSessionService sessionService;

    @Autowired
    private TAgentSessionServiceImpl sessionServiceImpl;

    @Autowired
    private TAgentMessageService messageService;

    @Autowired
    private TAgentNodeFlowService nodeFlowService;

    @Autowired
    private PredictServiceImpl predictService;

    @MdpConfig("agent_code2biz_code:{}")
    public HashMap<String,String> AGENT_BIZ_CODE_MAP;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将emitter添加到连接池
     */
    private void addEmitterToPool(String sessionId, String misId, SseEmitter emitter) {
        sessionEmitterPools.computeIfAbsent(sessionId, k -> ConcurrentHashMap.newKeySet()).add(emitter);
        emitterConnectionMap.put(emitter, new ConnectionInfo(sessionId, misId));
        log.info("添加SSE连接到池: sessionId={}, misId={}, 当前池大小={}",
                sessionId, misId, sessionEmitterPools.get(sessionId).size());
    }

    /**
     * 检查是否已存在到Python后端的连接
     */
    private boolean hasActiveBackendConnection(String sessionId) {
        BackendConnectionInfo connectionInfo = backendConnections.get(sessionId);
        return connectionInfo != null && connectionInfo.isActive();
    }

    /**
     * 添加Python后端连接信息
     */
    private void addBackendConnection(String sessionId, String misId, String agentCode) {
        BackendConnectionInfo connectionInfo = new BackendConnectionInfo(sessionId, misId, agentCode);
        backendConnections.put(sessionId, connectionInfo);
        log.info("添加Python后端连接信息: sessionId={}, misId={}, agentCode={}", sessionId, misId, agentCode);
    }

    /**
     * 移除Python后端连接信息
     */
    private void removeBackendConnection(String sessionId) {
        BackendConnectionInfo connectionInfo = backendConnections.remove(sessionId);
        if (connectionInfo != null) {
            log.info("移除Python后端连接信息: sessionId={}, misId={}", sessionId, connectionInfo.getMisId());
        }
    }

    /**
     * 保存Session的start_message信息
     */
    private void saveStartMessage(String sessionId, String eventName, String eventId, String data) {
        StartMessageInfo startMessageInfo = new StartMessageInfo(sessionId, eventName, eventId, data);
        sessionStartMessages.put(sessionId, startMessageInfo);
        log.info("保存start_message: sessionId={}", sessionId);
    }

    /**
     * 获取Session的start_message信息
     */
    private StartMessageInfo getStartMessage(String sessionId) {
        return sessionStartMessages.get(sessionId);
    }

    /**
     * 移除Session的start_message信息
     */
    private void removeStartMessage(String sessionId) {
        StartMessageInfo startMessageInfo = sessionStartMessages.remove(sessionId);
        if (startMessageInfo != null) {
            log.info("移除start_message: sessionId={}", sessionId);
        }
    }

    /**
     * 推送缓存的start_message给新连接
     */
    private void pushCachedStartMessage(String sessionId) {
        StartMessageInfo startMessageInfo = getStartMessage(sessionId);
        if (startMessageInfo != null) {
            log.info("推送缓存的start_message: sessionId={}", sessionId);
            broadcastToSessionPool(sessionId, startMessageInfo.getEventName(),
                                 startMessageInfo.getEventId(), startMessageInfo.getData());
        } else {
            log.debug("没有找到缓存的start_message: sessionId={}", sessionId);
        }
    }

    /**
     * 标记Python后端连接为非活跃状态
     */
    private void markBackendConnectionInactive(String sessionId) {
        BackendConnectionInfo connectionInfo = backendConnections.get(sessionId);
        if (connectionInfo != null) {
            connectionInfo.setActive(false);
            log.info("标记Python后端连接为非活跃: sessionId={}", sessionId);
        }
    }

    /**
     * 建立到Python后端的连接（通用方法）
     * @param sessionId 会话ID
     * @param misId 用户ID
     * @param agentCode Agent代码
     * @param emitter 前端连接（可为null，表示仅建立后端连接）
     * @param extra 额外参数
     */
    private void establishPythonBackendConnection(String sessionId, String misId, String agentCode,
                                                  SseEmitter emitter, Map<String, Object> extra) {
        // 获取Agent信息
        Map<String, String> agentInfo = getAgentInfo(agentCode);
        if (agentInfo == null) {
            log.error("获取Agent信息失败: sessionId={}, agentCode={}", sessionId, agentCode);
            if (emitter != null) {
                markBackendConnectionInactive(sessionId);
            }
            return;
        }

        String originalIp = agentInfo.get("ip");
        String nodeList = agentInfo.get("nodeInfos");
        Map<String, Object> finalExtra = extra != null ? extra : new HashMap<>();
        finalExtra.put("nodeInfos", nodeList);

        // 获取要使用的IP：优先使用Tair中的映射关系，否则使用新获取的IP
        String targetIp = sessionServiceImpl.getTargetIpForSession(sessionId, originalIp);

        backendSSEPool.execute(() -> {
            HttpURLConnection connection = null;
            String currentIp = targetIp;
            boolean connectionSuccess = false;

            try {
                // 尝试连接，如果失败则重试使用原始IP
                for (int attempt = 1; attempt <= 2; attempt++) {
                    try {
                        connection = createBackendConnection(sessionId, misId, currentIp, attempt);
                        connectionSuccess = true;
                        // 连接成功后更新IP映射关系
                        sessionServiceImpl.updateSessionIpMapping(sessionId, currentIp);
                        break;
                    } catch (Exception e) {
                        log.error("第{}次连接失败: sessionId={}, 错误: ", attempt, sessionId, e);
                        if (connection != null) {
                            connection.disconnect();
                            connection = null;
                        }

                        if (attempt == 1 && !currentIp.equals(originalIp)) {
                            // 第一次失败且当前IP不是原始IP，尝试使用原始IP重试
                            log.info("使用原始IP重试连接: sessionId={}, originalIp={}", sessionId, originalIp);
                            currentIp = originalIp;
                        } else {
                            // 第二次失败或当前IP就是原始IP，抛出异常
                            throw e;
                        }
                    }
                }

                if (!connectionSuccess || connection == null) {
                    throw new IOException("连接失败，已尝试所有可用IP: sessionId=" + sessionId);
                }

                // 处理SSE流
                processBackendSseStream(connection, sessionId, emitter, finalExtra);

            } catch (java.net.SocketTimeoutException e) {
                log.error("Python后端连接超时: sessionId={}, 错误信息: ", sessionId, e);
                handleBackendConnectionError(sessionId, emitter, e);
            } catch (java.net.ConnectException e) {
                log.error("Python后端连接失败: sessionId={}, 错误信息: ", sessionId, e);
                handleBackendConnectionError(sessionId, emitter, e);
            } catch (Exception e) {
                log.error("建立Python后端连接时出错: sessionId={}, 错误类型: {}, 错误信息: ",
                         sessionId, e.getClass().getSimpleName(), e);
                handleBackendConnectionError(sessionId, emitter, e);
            } finally {
                if (connection != null) {
                    connection.disconnect();
                }
                // 确保在连接结束时清理后端连接信息
                markBackendConnectionInactive(sessionId);
            }
        });
    }

    /**
     * 创建到Python后端的HTTP连接
     */
    private HttpURLConnection createBackendConnection(String sessionId, String misId, String ip, int attempt) throws Exception {
        String currentSseUrl = getRealSseUrl(ip, backendSseUrl);
        // 构建URL，添加参数
        StringBuilder urlBuilder = new StringBuilder(currentSseUrl);
        urlBuilder.append("?");
        if (sessionId != null && !sessionId.isEmpty()) {
            urlBuilder.append("session_id=").append(URLEncoder.encode(sessionId, StandardCharsets.UTF_8.name()));
        }
        urlBuilder.append("&");
        if (misId != null && !misId.isEmpty()) {
            urlBuilder.append("mis_id=").append(URLEncoder.encode(misId, StandardCharsets.UTF_8.name()));
        }

        URL url = new URL(urlBuilder.toString());
        log.info("尝试连接到后端SSE服务 (第{}次): sessionId={}, url={}", attempt, sessionId, url);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setDoInput(true);

        // 设置连接超时和读取超时
        connection.setConnectTimeout(30000);  // 连接超时30秒
        connection.setReadTimeout(600000);    // 读取超时10分钟
        log.info("设置后端连接超时参数: sessionId={}, connectTimeout=30s, readTimeout=10min", sessionId);

        // 设置接受事件流
        connection.setRequestProperty("Accept", "text/event-stream");

        // 测试连接
        connection.connect();
        int responseCode = connection.getResponseCode();
        if (responseCode == 200) {
            log.info("成功连接到后端SSE服务: sessionId={}, url={}", sessionId, url);
            return connection;
        } else {
            log.info("连接失败: sessionId={}, 响应码: {}", sessionId, responseCode);
            throw new IOException("HTTP响应码: " + responseCode);
        }
    }

    /**
     * 处理Python后端的SSE流
     */
    private void processBackendSseStream(HttpURLConnection connection, String sessionId,
                                         SseEmitter emitter, Map<String, Object> extra) throws Exception {
        log.info("开始处理Python后端SSE流: sessionId={}, 读取超时=10分钟", sessionId);
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            StringBuilder eventData = new StringBuilder();
            String eventName = "";
            String eventId = "";

            while ((line = reader.readLine()) != null) {
                log.info("接收到原始数据: sessionId={}, data={}", sessionId, line);

                if (line.isEmpty()) {
                    // 空行表示事件结束，处理并发送事件
                    if (eventData.length() > 0) {
                        String rawPythonData = eventData.toString();
                        String currentEventName = eventName;
                        String currentEventId = eventId;

                        log.info("从Python后端接收到完整事件: sessionId={}, eventName={}, eventId={}", sessionId, currentEventName, currentEventId);

                        // 处理/保存数据
                        String dataForFrontend = processEventData(rawPythonData, currentEventName, extra);

                        // 如果是start_message，缓存起来用于复用连接时推送
                        if ("start_message".equals(currentEventName) && dataForFrontend != null && !dataForFrontend.isEmpty()) {
                            saveStartMessage(sessionId, currentEventName, currentEventId, dataForFrontend);
                        }

                        // 如果有前端连接，则转发数据
                        if (emitter != null && dataForFrontend != null && !dataForFrontend.isEmpty()) {
                            broadcastToSessionPool(sessionId, currentEventName, currentEventId, dataForFrontend);
                        }

                        // 检查是否需要主动断开连接
                        if ("finish_message".equals(currentEventName) || "error_message".equals(currentEventName)) {
                            log.info("收到结束/错误消息, 主动断开连接: sessionId={}, eventName={}", sessionId, currentEventName);
                            markBackendConnectionInactive(sessionId);
                            if (emitter != null) {
                                closeConnectionsAfterFinalMessage(sessionId, emitter, currentEventName);
                            }
                            break;
                        }

                        // 重置事件数据
                        eventData.setLength(0);
                        eventName = "";
                        eventId = "";
                    }
                } else if (line.startsWith("event:")) {
                    eventName = line.substring(6).trim();
                } else if (line.startsWith("id:")) {
                    eventId = line.substring(3).trim();
                } else if (line.startsWith("data:")) {
                    eventData.append(line.substring(5).trim()).append("\n");
                }
            }

            // 处理最后一个事件（如果有）
            if (eventData.length() > 0) {
                String rawPythonData = eventData.toString();
                String currentEventName = eventName;
                String currentEventId = eventId;

                String dataForFrontend = processEventData(rawPythonData, currentEventName, extra);

                if (emitter != null && dataForFrontend != null && !dataForFrontend.isEmpty()) {
                    broadcastToSessionPool(sessionId, currentEventName, currentEventId, dataForFrontend);
                }

                if ("finish_message".equals(currentEventName) || "error_message".equals(currentEventName)) {
                    markBackendConnectionInactive(sessionId);
                    if (emitter != null) {
                        closeConnectionsAfterFinalMessage(sessionId, emitter, currentEventName);
                    }
                } else {
                    markBackendConnectionInactive(sessionId);
                    if (emitter != null) {
                        closeConnectionsAfterFinalMessage(sessionId, emitter, "normal_end");
                    }
                }
            }
        }
    }

    /**
     * 处理后端连接错误
     */
    private void handleBackendConnectionError(String sessionId, SseEmitter emitter, Exception e) {
        try {
            // 标记后端连接为非活跃
            markBackendConnectionInactive(sessionId);
            if (emitter != null) {
                String errorMessage;
                if (e instanceof java.net.SocketTimeoutException) {
                    if (e.getMessage() != null && e.getMessage().contains("connect")) {
                        errorMessage = "连接Python后端服务超时(30秒)，请稍后重试";
                        log.warn("Python后端连接超时: sessionId={}", sessionId);
                    } else {
                        errorMessage = "Python后端服务读取超时(10分钟)，可能服务处理时间过长";
                        log.warn("Python后端读取超时: sessionId={}", sessionId);
                    }
                } else if (e instanceof java.net.ConnectException) {
                    errorMessage = "无法连接到Python后端服务，请检查服务状态";
                    log.warn("Python后端连接失败: sessionId={}", sessionId);
                } else {
                    errorMessage = "Python后端服务异常: " + e.getMessage();
                    log.warn("Python后端服务异常: sessionId={}, 错误: ", sessionId, e);
                }

                // 向该sessionId的所有连接广播错误消息
                broadcastToSessionPool(sessionId, "error", String.valueOf(System.currentTimeMillis()), errorMessage);
                // 关闭所有连接
                closeConnectionsAfterFinalMessage(sessionId, emitter, "exception");
            }
        } catch (Exception ex) {
            log.error("处理后端连接错误时发生异常: sessionId={}", sessionId, ex);
        }
    }

    /**
     * 从连接池中移除emitter
     */
    private void removeEmitterFromPool(SseEmitter emitter) {
        ConnectionInfo connectionInfo = emitterConnectionMap.remove(emitter);
        if (connectionInfo != null) {
            String sessionId = connectionInfo.getSessionId();
            Set<SseEmitter> emitters = sessionEmitterPools.get(sessionId);
            if (emitters != null) {
                boolean removed = emitters.remove(emitter);
                if (removed) {
                    log.info("成功从连接池移除SSE连接: sessionId={}, misId={}",
                            sessionId, connectionInfo.getMisId());
                }
                if (emitters.isEmpty()) {
                    sessionEmitterPools.remove(sessionId);
                    // 停止并清理心跳检测任务
                    stopHeartbeatForSession(sessionId);
                    log.info("移除空的连接池并停止心跳检测: sessionId={}", sessionId);
                } else {
                    log.info("连接池剩余连接数: sessionId={}, 剩余={}", sessionId, emitters.size());
                }
            }
        } else {
            log.info("尝试移除连接但未找到连接信息: emitter={}", emitter);
        }
    }

    /**
     * 向指定sessionId的所有连接广播消息
     */
    private void broadcastToSessionPool(String sessionId, String eventName, String eventId, String data) {
        Set<SseEmitter> emitters = sessionEmitterPools.get(sessionId);
        if (emitters == null || emitters.isEmpty()) {
            if ("heartbeat".equals(eventName)) {
                log.info("sessionId={} 的连接池为空，跳过心跳广播", sessionId);
            } else {
                log.debug("sessionId={} 的连接池为空，跳过广播", sessionId);
            }
            return;
        }

        if ("heartbeat".equals(eventName)) {
            log.info("向sessionId={} 的 {} 个连接发送心跳消息", sessionId, emitters.size());
        } else {
            log.debug("向sessionId={} 的 {} 个连接广播消息: {}", sessionId, emitters.size(), eventName);
        }

        // 创建要移除的emitter列表，避免在迭代时修改集合
        Set<SseEmitter> toRemove = ConcurrentHashMap.newKeySet();
        int successCount = 0;

        for (SseEmitter emitter : emitters) {
            try {
                SseEmitter.SseEventBuilder sseEventBuilder = SseEmitter.event()
                        .name(eventName)
                        .id(eventId.isEmpty() ? String.valueOf(System.currentTimeMillis()) : eventId)
                        .data(data);
                emitter.send(sseEventBuilder);
                successCount++;
                if ("heartbeat".equals(eventName)) {
                    log.info("成功向sessionId={} 的一个连接发送心跳", sessionId);
                } else {
                    log.debug("成功向sessionId={} 的一个连接发送消息", sessionId);
                }
            } catch (IOException e) {
                log.warn("向sessionId={} 的连接发送消息失败 (IOException - 连接已断开): ", sessionId, e);
                toRemove.add(emitter);
            } catch (IllegalStateException e) {
                log.warn("向sessionId={} 的连接发送消息失败 (IllegalStateException - 连接状态异常): ", sessionId, e);
                toRemove.add(emitter);
            } catch (Exception e) {
                log.error("向sessionId={} 的连接发送消息时发生意外错误: {}", sessionId, e.getMessage(), e);
                toRemove.add(emitter);
            }
        }

        // 移除失败的连接
        if (!toRemove.isEmpty()) {
            log.warn("检测到sessionId={} 有 {} 个断开的连接，正在清理", sessionId, toRemove.size());
            for (SseEmitter emitter : toRemove) {
                removeEmitterFromPool(emitter);
            }
        }

        if ("heartbeat".equals(eventName)) {
            log.info("心跳广播完成: sessionId={}, 成功={}, 失败={}", sessionId, successCount, toRemove.size());
        }
    }

    /**
     * @param agent_code Agent代码（必填）
     * @return SseEmitter 实例
     */
    public SseEmitter connect(String session_id, String mis_id, String agent_code) {
        // 检查必填参数
        if (mis_id == null || mis_id.isEmpty() || agent_code == null || agent_code.isEmpty()) {
            log.error("连接参数校验失败: sessionId={}, misId={}, agentCode={}", session_id, mis_id, agent_code);
            SseEmitter emitter = new SseEmitter(3600000L);
            try {
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("连接失败：用户ID或Agent代码不能为空")
                        .id(String.valueOf(System.currentTimeMillis())));
                emitter.complete();
            } catch (Exception e) {
                log.error("发送连接失败消息异常: sessionId={}", session_id, e);
                emitter.completeWithError(e);
            }
            return emitter;
        }

        // 如果session_id为空，则创建新会话
        if (session_id == null || session_id.isEmpty()) {
            try {
                // 调用sessionService创建新会话
                TStringResponse response = sessionService.createSession(mis_id, agent_code);
                if (response != null && ResponseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && response.getData() != null) {
                    session_id = response.getData();
                    log.info("为用户创建新会话: misId={}, sessionId={}", mis_id, session_id);
                } else {
                    log.error("创建会话失败: misId={}, 错误: {}", mis_id, response != null ? response.getMessage() : "未知错误");
                }
            } catch (Exception e) {
                log.error("创建会话异常: misId={}", mis_id, e);
            }
        }

        final String finalSessionId = session_id;
        final String finalMisId = mis_id;

        // 创建一个超时时间为1小时的SseEmitter
        SseEmitter emitter = new SseEmitter(3600000L);

        // 注册连接完成或超时的回调，用于清理资源
        emitter.onCompletion(() -> {
            removeEmitterFromPool(emitter);
            log.info("SSE连接已完成: sessionId={}, misId={}", finalSessionId, finalMisId);
        });
        emitter.onTimeout(() -> {
            removeEmitterFromPool(emitter);
            log.info("SSE连接超时: sessionId={}, misId={}", finalSessionId, finalMisId);
        });
        emitter.onError(e -> {
            removeEmitterFromPool(emitter);
            log.error("SSE连接出错: sessionId={}, misId={}", finalSessionId, finalMisId, e);
        });

        // 将emitter添加到连接池
        addEmitterToPool(finalSessionId, finalMisId, emitter);

        // 启动心跳检测
        startHeartbeatForSession(finalSessionId);

        // 检查是否已存在到Python后端的连接，避免重复建立
        if (hasActiveBackendConnection(finalSessionId)) {
            log.info("sessionId={} 本地标记存在活跃的Python后端连接，验证真实连接状态", finalSessionId);

            // 验证后端连接的真实状态
            Map<String, Object> connectionStatus = checkAndRefreshConnection(finalSessionId, false);
            boolean backendConnectionExists = connectionStatus != null &&
                Boolean.TRUE.equals(connectionStatus.get("exists")) &&
                !"error".equals(connectionStatus.get("status"));

            if (backendConnectionExists) {
                log.info("sessionId={} 后端连接验证成功，复用现有连接", finalSessionId);
                // 复用连接时，主动推送缓存的start_message给新连接的前端
                pushCachedStartMessage(finalSessionId);
                return emitter;
            } else {
                log.warn("sessionId={} 后端连接验证失败，清理本地状态并重新建立连接", finalSessionId);
                // 清理本地的后端连接状态
                markBackendConnectionInactive(finalSessionId);
                removeBackendConnection(finalSessionId);
            }
        }

        // 添加Python后端连接信息
        addBackendConnection(finalSessionId, finalMisId, agent_code);

        // 使用通用方法建立Python后端连接
        establishPythonBackendConnection(finalSessionId, finalMisId, agent_code, emitter, null);
        return emitter;
    }

    private Map<String, String> getAgentInfo(String agentCode) {
        try {
            TPredictRequest predictRequest = new TPredictRequest();
            predictRequest.setBizCode(AGENT_BIZ_CODE_MAP.get(agentCode));
            predictRequest.setReq(new java.util.HashMap<>());
            TPredictResponse predictResponse = predictService.predict(predictRequest);
            log.info("调用predictService.predict接口: agentCode={}, predictRequest={}, predictResponse={}",
                    agentCode, JSON.toJSONString(predictRequest), JSON.toJSONString(predictResponse));
            // 可根据需要处理predictResponse
            if (predictResponse == null || predictResponse.getData() == null) {
                throw new Exception("无返回结果");
            }
            Map<String, String> data = predictResponse.getData();
            return data;
        } catch (Exception e) {
            log.error("getAgentInfo异常: agentCode={}", agentCode, e);
        }
        return null;
    }



    private String getRealSseUrl(String ip, String baseUrl) {
        try {
            // 使用获取到的IP替换chatUrl中的IP
            String actualChatUrl = baseUrl.replaceAll("(http://|https://)[^:/]+", "$1" + ip);
            return actualChatUrl;
        } catch (Exception e) {
            log.error("替换RealSSEUrl异常: ip={}, baseUrl={}", ip, baseUrl, e);
        }
        return null;
    }

    /**
     * 关闭指定sessionId的所有连接
     *
     * @param sessionId 会话ID
     * @return 关闭结果
     */
    public String closeConnection(String sessionId) {
        Set<SseEmitter> emitters = sessionEmitterPools.get(sessionId);
        if (emitters != null && !emitters.isEmpty()) {
            int closedCount = 0;
            for (SseEmitter emitter : emitters) {
                try {
                    emitter.complete();
                    closedCount++;
                } catch (Exception e) {
                    log.warn("关闭连接时出现异常: sessionId={}", sessionId, e);
                }
            }
            sessionEmitterPools.remove(sessionId);
            // 清理连接信息映射
            emitterConnectionMap.entrySet().removeIf(entry ->
                sessionId.equals(entry.getValue().getSessionId()));

            // 停止心跳检测
            stopHeartbeatForSession(sessionId);

            // 删除Tair中的Session与IP映射关系
            sessionServiceImpl.removeSessionIpMapping(sessionId);

            // 清理Python后端连接信息
            removeBackendConnection(sessionId);

            // 清理start_message缓存
            removeStartMessage(sessionId);

            return "Closed " + closedCount + " connections for sessionId: " + sessionId;
        }
        return "No connections found for sessionId: " + sessionId;
    }

    /**
     * 主动关闭前端连接并中断Python连接循环
     *
     * @param sessionId 会话ID
     * @param emitter SSE发射器（当前处理的emitter，可能为null）
     * @param eventName 触发关闭的事件名称
     */
    private void closeConnectionsAfterFinalMessage(String sessionId, SseEmitter emitter, String eventName) {
        try {
            log.info("因收到{}消息，主动关闭sessionId={}的所有连接", eventName, sessionId);

            // 关闭该sessionId的所有前端连接
            Set<SseEmitter> emitters = sessionEmitterPools.get(sessionId);
            if (emitters != null && !emitters.isEmpty()) {
                int closedCount = 0;
                for (SseEmitter em : emitters) {
                    try {
                        em.complete();
                        closedCount++;
                    } catch (Exception e) {
                        log.warn("关闭单个连接时出现异常: sessionId={}", sessionId, e);
                    }
                }
                sessionEmitterPools.remove(sessionId);
                // 清理连接信息映射
                emitterConnectionMap.entrySet().removeIf(entry ->
                    sessionId.equals(entry.getValue().getSessionId()));
                // 停止心跳检测
                stopHeartbeatForSession(sessionId);
                log.info("已关闭sessionId={}的{}个SSE连接", sessionId, closedCount);
            } else {
                log.info("sessionId={}没有活跃的SSE连接需要关闭", sessionId);
            }

            // 删除Tair中的Session与IP映射关系
            sessionServiceImpl.removeSessionIpMapping(sessionId);

            // 清理Python后端连接信息
            removeBackendConnection(sessionId);

            // 清理start_message缓存
            removeStartMessage(sessionId);
        } catch (Exception e) {
            log.error("关闭连接时发生异常: sessionId={}", sessionId, e);
        }
    }

    /**
     * 获取当前活跃连接数
     *
     * @return 活跃连接数
     */
    public int getActiveConnectionsCount() {
        return sessionEmitterPools.values().stream()
                .mapToInt(Set::size)
                .sum();
    }

    /**
     * 获取当前活跃会话数
     *
     * @return 活跃会话数
     */
    public int getActiveSessionsCount() {
        return sessionEmitterPools.size();
    }

    /**
     * 检查并刷新SSE连接状态（调用Python端接口）
     *
     * @param sessionId 会话ID
     * @param autoRefresh 是否自动刷新活动时间
     * @return 连接状态信息
     */
    public Map<String, Object> checkAndRefreshConnection(String sessionId, boolean autoRefresh) {
        try {
            // 首先获取Agent信息以确定目标IP
            String targetIp = getTargetIpForSession(sessionId);
            if (targetIp == null) {
                log.error("无法获取sessionId={} 的目标IP", sessionId);
                return createErrorResponse(sessionId, "无法获取目标IP");
            }

            // 构建Python端的检查接口URL
            String checkUrl = backendConnectionsCheckUrl.replaceAll("(http://|https://)[^:/]+", "$1" + targetIp);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("sessionId", sessionId);
            requestBody.put("autoRefresh", autoRefresh);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            log.info("调用Python端连接检查接口: url={}, sessionId={}, autoRefresh={}",
                    checkUrl, sessionId, autoRefresh);

            // 调用Python端接口
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.postForEntity(checkUrl, request, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> result = response.getBody();
                log.info("Python端连接检查成功: sessionId={}, exists={}, connectionCount={}",
                        sessionId, result.get("exists"), result.get("connectionCount"));
                return result;
            } else {
                log.error("Python端连接检查失败: sessionId={}, statusCode={}",
                        sessionId, response.getStatusCode());
                return createErrorResponse(sessionId, "Python端接口调用失败");
            }

        } catch (Exception e) {
            log.error("调用Python端连接检查接口时发生异常: sessionId={}", sessionId, e);
            return createErrorResponse(sessionId, "接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 获取Session对应的目标IP
     */
    private String getTargetIpForSession(String sessionId) {
        try {
            // 先从本地连接池获取会话信息
            Set<SseEmitter> emitters = sessionEmitterPools.get(sessionId);
            if (emitters != null && !emitters.isEmpty()) {
                // 如果有本地连接，说明已经有目标IP，可以从Tair获取
                return sessionServiceImpl.getTargetIpForSession(sessionId, null);
            }

            // 如果没有本地连接，需要通过会话信息获取agentCode，然后获取IP
            TSessionResponse sessionResp = sessionService.getBySessionId(sessionId);
            if (sessionResp != null && sessionResp.getData() != null) {
                String agentCode = sessionResp.getData().getAgentCode();
                Map<String, String> agentInfo = getAgentInfo(agentCode);
                if (agentInfo != null) {
                    String originalIp = agentInfo.get("ip");
                    // 优先使用Tair中的映射关系
                    return sessionServiceImpl.getTargetIpForSession(sessionId, originalIp);
                }
            }

            log.warn("无法获取sessionId={} 的目标IP", sessionId);
            return null;

        } catch (Exception e) {
            log.error("获取目标IP时发生异常: sessionId={}", sessionId, e);
            return null;
        }
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String sessionId, String errorMessage) {
        Map<String, Object> result = new HashMap<>();
        result.put("sessionId", sessionId);
        result.put("exists", false);
        result.put("connectionCount", 0);
        result.put("aliveConnections", 0);
        result.put("refreshed", false);
        result.put("refreshedCount", 0);
        result.put("connections", new java.util.ArrayList<>());
        result.put("timestamp", System.currentTimeMillis() / 1000.0);
        result.put("status", "error");
        result.put("error", errorMessage);
        return result;
    }

    /**
     * 确保Session有到Python服务的后端连接，如果没有则主动建立连接
     * 注意：这只建立后端连接，不创建前端SseEmitter
     *
     * @param sessionId 会话ID
     * @param misId 用户ID
     * @param agentCode Agent代码
     * @return 是否成功确保连接存在
     */
    public boolean ensureBackendConnection(String sessionId, String misId, String agentCode) {
        // 先检查是否已有前端连接（如果有前端连接，说明后端连接也存在）
        Set<SseEmitter> emitters = sessionEmitterPools.get(sessionId);
        if (emitters != null && !emitters.isEmpty()) {
            log.info("Session {} 已有 {} 个活跃的前端连接，后端连接应该也存在", sessionId, emitters.size());
            return true;
        }

        log.info("Session {} 没有活跃连接，主动建立到Python服务的后端连接", sessionId);

        try {
            // 只建立后端连接，不创建前端SseEmitter
            return establishBackendConnection(sessionId, misId, agentCode);
        } catch (Exception e) {
            log.error("为Session {} 建立后端连接时发生异常", sessionId, e);
            return false;
        }
    }

    /**
     * 建立到Python服务的后端连接（不创建前端SseEmitter）
     *
     * @param sessionId 会话ID
     * @param misId 用户ID
     * @param agentCode Agent代码
     * @return 是否成功建立连接
     */
    private boolean establishBackendConnection(String sessionId, String misId, String agentCode) {
        try {
            // 检查是否已存在到Python后端的连接，避免重复建立
            if (hasActiveBackendConnection(sessionId)) {
                log.info("sessionId={} 本地标记存在活跃的Python后端连接，验证真实连接状态", sessionId);

                // 验证后端连接的真实状态
                Map<String, Object> connectionStatus = checkAndRefreshConnection(sessionId, false);
                boolean backendConnectionExists = connectionStatus != null &&
                    Boolean.TRUE.equals(connectionStatus.get("exists")) &&
                    !"error".equals(connectionStatus.get("status"));

                if (backendConnectionExists) {
                    log.info("sessionId={} 后端连接验证成功，复用现有连接", sessionId);
                    return true;
                } else {
                    log.warn("sessionId={} 后端连接验证失败，清理本地状态并重新建立连接", sessionId);
                    // 清理本地的后端连接状态
                    markBackendConnectionInactive(sessionId);
                    removeBackendConnection(sessionId);
                }
            }

            // 添加Python后端连接信息
            addBackendConnection(sessionId, misId, agentCode);

            // 使用通用方法建立Python后端连接（不传递前端emitter）
            establishPythonBackendConnection(sessionId, misId, agentCode, null, null);

            log.info("成功启动后端连接任务: sessionId={}", sessionId);
            return true;

        } catch (Exception e) {
            log.error("建立后端连接失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    /**
     * 启动Session的心跳检测
     * 定期发送心跳消息来检测客户端连接状态
     *
     * @param sessionId 会话ID
     */
    private void startHeartbeatForSession(String sessionId) {
        // 避免重复启动心跳检测
        if (heartbeatTasks.containsKey(sessionId)) {
            log.info("sessionId={} 的心跳检测已存在，跳过启动", sessionId);
            return;
        }

        log.info("启动sessionId={} 的心跳检测", sessionId);
        ScheduledFuture<?> heartbeatTask = heartBeatPool.scheduleWithFixedDelay(() -> {
            Set<SseEmitter> emitters = sessionEmitterPools.get(sessionId);
            if (emitters == null || emitters.isEmpty()) {
                // 连接池为空，停止心跳检测
                log.info("sessionId={} 连接池为空，停止心跳检测", sessionId);
                stopHeartbeatForSession(sessionId);
                return;
            }

            log.info("发送心跳检测: sessionId={}, 连接数={}", sessionId, emitters.size());

            // 发送心跳消息，这会自动检测并清理断开的连接
            try {
                broadcastToSessionPool(sessionId, "heartbeat", String.valueOf(System.currentTimeMillis()), "ping");
                log.info("心跳检测完成: sessionId={}", sessionId);
            } catch (Exception e) {
                log.warn("心跳检测失败: sessionId={}", sessionId, e);
            }
        }, 30, 30, TimeUnit.SECONDS); // 每30秒发送一次心跳

        // 保存任务引用
        heartbeatTasks.put(sessionId, heartbeatTask);
    }

    /**
     * 停止Session的心跳检测
     *
     * @param sessionId 会话ID
     */
    private void stopHeartbeatForSession(String sessionId) {
        ScheduledFuture<?> task = heartbeatTasks.remove(sessionId);
        if (task != null) {
            task.cancel(false);
            log.info("已停止sessionId={} 的心跳检测任务", sessionId);
        }
    }

    /**
     * 处理事件数据，可以在这里对消息进行加工处理
     *
     * @param data      原始数据
     * @param eventName 事件名称
     * @return 处理后的数据
     */
    private String processEventData(String data, String eventName, Map<String, Object> extra) {
        try {
            if (data != null && !data.isEmpty()) {
                Map<String, Object> jsonMap = objectMapper.readValue(data, new TypeReference<Map<String, Object>>() {});
                if (jsonMap.containsKey("data") && jsonMap.get("data") instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> dataMap = (Map<String, Object>) jsonMap.get("data");
                    String sessionId = dataMap.get("sessionId") != null ? String.valueOf(dataMap.get("sessionId")) : null;
                    if ("origin_message".equals(eventName)) {
                        // 原始消息只保存原始消息，不转发
                        saveOriginalMessage(dataMap);
                        // 不转发消息，直接返回空字符串
                        return "";
                    } else if ("finish_message".equals(eventName)) {
                        // 结束消息，更新session状态为执行完成
                        if (sessionId != null) {
                            sessionService.updateSessionStatus(sessionId, FINISHED.getCode());
                        }
                    } else if ("error_message".equals(eventName)) {
                        // 错误消息，更新session状态为执行错误
                        if (sessionId != null) {
                            sessionService.updateSessionStatus(sessionId, ERROR.getCode());
                        }
                    } else if ("show_message".equals(eventName)) {
                        // 保持原有逻辑
                        saveMessage(dataMap);
                    } else if ("node_message".equals(eventName)) {
                        // 处理流程消息，保存节点流转信息
                        saveNodeFlow(dataMap);
                    } else if ("start_message".equals(eventName)) {
                        if (extra != null && extra.containsKey("nodeInfos")) {
                            dataMap.put("nodeInfos", extra.get("nodeInfos"));
                            data = JSONObject.toJSONString(jsonMap);
                        }
                    } else if ("request_input_message".equals(eventName)) {
                        if (sessionId != null) {
                            sessionService.updateSessionStatus(sessionId, WAITCHAT.getCode());
                        }
                    } else if ("pending_message".equals(eventName)) {
                        if (sessionId != null) {
                            sessionService.updateSessionStatus(sessionId, PENDING.getCode());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理事件数据异常: eventName={}", eventName, e);
        }
        // 默认返回原始数据
        return data != null ? data.trim() : "";
    }

    /**
     * 保存原始消息到数据库
     *
     * @param dataMap 原始消息数据Map
     */
    private void saveOriginalMessage(Map<String, Object> dataMap) {
        try {
            AgentOriginalMessageDTO originalMessageDTO = new AgentOriginalMessageDTO();
            if (dataMap.get("msgId") != null) {
                originalMessageDTO.setMsgId(String.valueOf(dataMap.get("msgId")));
            }
            if (dataMap.get("sessionId") != null) {
                originalMessageDTO.setSessionId(String.valueOf(dataMap.get("sessionId")));
            }
            if (dataMap.get("source") != null) {
                originalMessageDTO.setSource(String.valueOf(dataMap.get("source")));
            }
            if (dataMap.get("modelUsage") != null) {
                originalMessageDTO.setModelUsage(String.valueOf(dataMap.get("modelUsage")));
            }
            if (dataMap.get("type") != null) {
                originalMessageDTO.setType(String.valueOf(dataMap.get("type")));
            }
            if (dataMap.get("metaData") != null) {
                originalMessageDTO.setMetaData(String.valueOf(dataMap.get("metaData")));
            }
            if (dataMap.get("content") != null) {
                originalMessageDTO.setContent(String.valueOf(dataMap.get("content")));
            }
            messageService.saveSingleOriginalMessage(originalMessageDTO);
        } catch (Exception e) {
            log.error("保存原始消息异常: sessionId={}, msgId={}",
                    dataMap.get("sessionId"), dataMap.get("msgId"), e);
        }
    }

    /**
     * 保存消息到数据库
     *
     * @param dataMap 消息数据Map
     */
    private void saveMessage(Map<String, Object> dataMap) {
        try {
            AgentMessageDTO messageDTO = new AgentMessageDTO();
            // 设置消息属性
            if (dataMap.containsKey("sessionId")) {
                messageDTO.setSessionId(String.valueOf(dataMap.get("sessionId")));
            }
            if (dataMap.containsKey("msgId")) {
                messageDTO.setMsgId(String.valueOf(dataMap.get("msgId")));
            }
            if (dataMap.containsKey("messageType")) {
                messageDTO.setMessageType(String.valueOf(dataMap.get("messageType")));
            }
            if (dataMap.containsKey("source")) {
                messageDTO.setSource(String.valueOf(dataMap.get("source")));
            }
            if (dataMap.containsKey("content")) {
                messageDTO.setContent(String.valueOf(dataMap.get("content")));
            }
            // 调用消息服务保存消息
            TCheckResponse response = messageService.saveSingleMessage(messageDTO);
            if (response != null && ResponseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && Boolean.TRUE.equals(response.getData())) {
                log.info("消息保存成功: sessionId={}, msgId={}", messageDTO.getSessionId(), messageDTO.getMsgId());
            } else {
                log.error("消息保存失败: sessionId={}, msgId={}, 错误: {}",
                        messageDTO.getSessionId(), messageDTO.getMsgId(), response != null ? response.getMessage() : "未知错误");
            }
        } catch (Exception e) {
            log.error("保存消息异常: sessionId={}, msgId={}",
                    dataMap.get("sessionId"), dataMap.get("msgId"), e);
        }
    }

    /**
     * 保存节点流转信息到数据库
     *
     * @param dataMap 节点流转数据Map
     */
    private void saveNodeFlow(Map<String, Object> dataMap) {
        try {
            AgentNodeFlowDTO nodeFlowDTO = new AgentNodeFlowDTO();
            // 设置节点流转属性
            if (dataMap.containsKey("sessionId")) {
                nodeFlowDTO.setSessionId(String.valueOf(dataMap.get("sessionId")));
            }
            if (dataMap.containsKey("msgId")) {
                nodeFlowDTO.setMsgId(String.valueOf(dataMap.get("msgId")));
            }
            if (dataMap.containsKey("currentNodeName")) {
                nodeFlowDTO.setCurrentNodeName(String.valueOf(dataMap.get("currentNodeName")));
            }
            if (dataMap.containsKey("nextNodeName")) {
                nodeFlowDTO.setNextNodeName(String.valueOf(dataMap.get("nextNodeName")));
            }
            if (dataMap.containsKey("currentNodeStatus")) {
                nodeFlowDTO.setCurrentNodeStatus(String.valueOf(dataMap.get("currentNodeStatus")));
            }
            if (dataMap.containsKey("nodeLog")) {
                nodeFlowDTO.setNodeLog(String.valueOf(dataMap.get("nodeLog")));
            }

            // 调用节点流转服务保存记录
            TCheckResponse response = nodeFlowService.saveSingleNodeFlow(nodeFlowDTO);
            if (response != null && ResponseCodeEnum.SUCCESS.getCode().equals(response.getCode()) && Boolean.TRUE.equals(response.getData())) {
                log.info("节点流转记录保存成功: sessionId={}, msgId={}, currentNode={}",
                        nodeFlowDTO.getSessionId(), nodeFlowDTO.getMsgId(), nodeFlowDTO.getCurrentNodeName());
            } else {
                log.error("节点流转记录保存失败: sessionId={}, msgId={}, 错误: {}",
                        nodeFlowDTO.getSessionId(), nodeFlowDTO.getMsgId(), response != null ? response.getMessage() : "未知错误");
            }
        } catch (Exception e) {
            log.error("保存节点流转记录异常: sessionId={}, msgId={}",
                    dataMap.get("sessionId"), dataMap.get("msgId"), e);
        }
    }
}