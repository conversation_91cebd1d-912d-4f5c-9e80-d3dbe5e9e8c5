package com.sankuai.algoplatform.agentapi.domain.repository;

import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState;

import java.util.List;

/**
 * AgentState数据访问对象接口
 */
public interface AgentStateRepository {
    
    /**
     * 根据ID查询状态
     * 
     * @param id 状态ID
     * @return 状态对象，如果不存在则返回null
     */
    AgentState getById(Long id);
    
    /**
     * 根据会话ID查询状态列表
     * 
     * @param sessionId 会话ID
     * @return 状态列表
     */
    List<AgentState> listBySessionId(String sessionId);
    
    /**
     * 根据会话ID和版本ID查询状态
     * 
     * @param sessionId 会话ID
     * @param versionId 版本ID
     * @return 状态对象，如果不存在则返回null
     */
    AgentState getBySessionIdAndVersionId(String sessionId, String versionId);
    
    /**
     * 插入新状态
     * 
     * @param agentState 状态对象
     * @return 影响的行数
     */
    int insert(AgentState agentState);
    
    /**
     * 更新状态
     * 
     * @param agentState 状态对象
     * @return 影响的行数
     */
    int update(AgentState agentState);
    
    /**
     * 根据ID删除状态
     * 
     * @param id 状态ID
     * @return 影响的行数
     */
    int deleteById(Long id);
    
    /**
     * 根据会话ID删除所有状态
     * 
     * @param sessionId 会话ID
     * @return 影响的行数
     */
    int deleteBySessionId(String sessionId);
    
    /**
     * 获取会话的最新状态
     * 
     * @param sessionId 会话ID
     * @return 最新状态对象，如果不存在则返回null
     */
    AgentState getLatestBySessionId(String sessionId);
}