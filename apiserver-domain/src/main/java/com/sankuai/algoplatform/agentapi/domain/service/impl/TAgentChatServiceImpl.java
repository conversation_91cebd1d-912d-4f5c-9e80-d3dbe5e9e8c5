package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.response.chat.TChatResponse;
import com.sankuai.algoplatform.agentapi.client.response.session.TSessionResponse;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentChatService;
import com.sankuai.algoplatform.agentapi.client.service.TAgentMessageService;
import com.sankuai.algoplatform.agentapi.client.service.TAgentSessionService;
import com.sankuai.algoplatform.agentapi.domain.enums.SessionStatusEnum;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
/**
 * Agent聊天服务实现
 * <AUTHOR>
 * @date 2025年05月28日
 */
@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TAgentChatServiceImpl implements TAgentChatService {
    // agentCode和bizCode的映射
    @Value("${agent.chat.url:http://127.0.0.1:8000/api/chat}")
    private String chatUrl;

    private final RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private TAgentMessageService TAgentMessageService;

    @Autowired
    private TAgentSessionService TAgentSessionService;

    @Autowired
    private PredictServiceImpl predictService;

    @Autowired
    private SseProxyService sseProxyService;

    @Autowired
    private TAgentSessionServiceImpl sessionServiceImpl;

    @MdpConfig("wakeup_pending_misId:[]")
    public ArrayList<String> WAKEUP_PENDING_MISID;

    @MdpConfig("agent_code2biz_code:{}")
    public HashMap<String,String> AGENT_BIZ_CODE_MAP;

    private Boolean isPendingMisId(String misId) {
        return WAKEUP_PENDING_MISID.contains(misId);
    }

    @Override
    public TChatResponse chat(String misId, AgentMessageDTO agentMessageDTO) {
        // 记录chat消息开始日志
        log.info("Chat消息开始处理 - 发送人: {}, 会话ID: {}, 消息内容: {}",
                misId, agentMessageDTO.getSessionId(),
                agentMessageDTO.getContent() != null ?
                    (agentMessageDTO.getContent().length() > 200 ?
                        agentMessageDTO.getContent().substring(0, 200) + "..." :
                        agentMessageDTO.getContent()) : "null");

        // 校验会话owner权限
        TSessionResponse sessionResp =
                TAgentSessionService.getBySessionId(agentMessageDTO.getSessionId());
        if (sessionResp == null || sessionResp.getData() == null) {
            log.warn("Chat消息处理失败 - 会话不存在 - 发送人: {}, 会话ID: {}", misId, agentMessageDTO.getSessionId());
            return TChatResponse.fail(500, "会话不存在");
        }
        String owner = sessionResp.getData().getOwner();
        String agentCode = sessionResp.getData().getAgentCode();
        Integer status = sessionResp.getData().getStatus();

        // 记录会话信息
        log.info("Chat消息会话信息 - 发送人: {}, 会话ID: {}, 会话所有者: {}, Agent代码: {}, 会话状态: {}",
                misId, agentMessageDTO.getSessionId(), owner, agentCode, SessionStatusEnum.getDescByCode(status));

        // 只允许WAITCHAT状态或PREPARE状态 以及定时任务的PENDING状态
        if (status == null || (status != SessionStatusEnum.WAITCHAT.getCode() && status != SessionStatusEnum.PRE_ALLOCATE.getCode()&& status != SessionStatusEnum.PENDING.getCode())) {
            log.warn("Chat消息处理失败 - 当前状态不允许对话 - 发送人: {}, 会话ID: {}, 会话状态: {}", misId, agentMessageDTO.getSessionId(), SessionStatusEnum.getDescByCode(status));
            return TChatResponse.fail(500, String.format("当前状态不允许对话 - 会话状态: %s", SessionStatusEnum.getDescByCode(status)));
        }
        if (status == SessionStatusEnum.PRE_ALLOCATE.getCode() || status == SessionStatusEnum.WAITCHAT.getCode()) {
            if (!misId.equals(owner)&&!isPendingMisId(misId)) {
                log.warn("Chat消息处理失败 - 无权限 - 发送人: {}, 会话ID: {}, 会话所有者: {}", misId, agentMessageDTO.getSessionId(), owner);
                return TChatResponse.fail(403,"无权限");
            }
        }
        // 会话状态处理：PENDING状态需要特殊处理
        if (status.equals(SessionStatusEnum.PENDING.getCode())) {
            // 只有系统任务账号才能唤醒PENDING状态的会话
            if (!isPendingMisId(misId)) {
                log.warn("Chat消息处理失败 - 无权限唤醒PENDING状态会话 - 发送人: {}, 会话ID: {}", misId, agentMessageDTO.getSessionId());
                return TChatResponse.fail(403,"无权限");
            }
        }

        StopWatch stopWatch = new StopWatch();

        // 检查并确保SSE连接存在
        stopWatch.start("check_sse_connection");
        try {
            // 检查当前SSE连接状态
            Map<String, Object> connectionStatus = sseProxyService.checkAndRefreshConnection(
                    agentMessageDTO.getSessionId(), true);

            boolean exists = (Boolean) connectionStatus.get("exists");
            int aliveConnections = (Integer) connectionStatus.get("aliveConnections");

            log.info("SSE连接状态检查: sessionId={}, exists={}, aliveConnections={}",
                    agentMessageDTO.getSessionId(), exists, aliveConnections);

            // 如果没有活跃连接，主动建立后端连接
            if (!exists || aliveConnections == 0) {
                log.info("检测到sessionId={} 没有活跃的SSE连接，主动建立后端连接", agentMessageDTO.getSessionId());
                boolean ensured = sseProxyService.ensureBackendConnection(
                        agentMessageDTO.getSessionId(), misId, agentCode);

                if (!ensured) {
                    log.warn("为sessionId={} 建立后端连接失败，但继续处理chat请求", agentMessageDTO.getSessionId());
                } else {
                    log.info("成功为sessionId={} 建立后端连接", agentMessageDTO.getSessionId());
                }
            } else {
                log.info("sessionId={} 已有 {} 个活跃的SSE连接", agentMessageDTO.getSessionId(), aliveConnections);
            }
        } catch (Exception e) {
            log.error("检查SSE连接状态时发生异常: sessionId={}", agentMessageDTO.getSessionId(), e);
            // 不因为SSE连接问题而中断chat请求，继续处理
        }
        stopWatch.stop();
        // 调用predict接口
        try {
            stopWatch.start("predict");
            TPredictRequest predictRequest = new TPredictRequest();
            predictRequest.setBizCode(AGENT_BIZ_CODE_MAP.get(agentCode));
            predictRequest.setReq(new java.util.HashMap<>());
            TPredictResponse predictResponse = predictService.predict(predictRequest);
            // 可根据需要处理predictResponse
            if (predictResponse == null || predictResponse.getData() == null) {
                return TChatResponse.fail(500, "获取Agent实现类路径失败");
            }
            Map<String,String> data = predictResponse.getData();
            String moduleName = data.get("module_name");
            String originalIp = data.get("ip");
            stopWatch.stop();

            stopWatch.start("get_target_ip");
            // 获取要使用的IP：优先使用Tair中的映射关系，否则使用新获取的IP（与SSE建联逻辑一致）
            String targetIp = sessionServiceImpl.getTargetIpForSession(agentMessageDTO.getSessionId(), originalIp);
            log.info("Chat请求IP选择: sessionId={}, originalIp={}, targetIp={}",
                    agentMessageDTO.getSessionId(), originalIp, targetIp);
            stopWatch.stop();

            stopWatch.start("save_message");
            // 使用目标IP替换chatUrl中的IP
            String actualChatUrl = chatUrl.replaceAll("(http://|https://)[^:/]+", "$1" + targetIp);

            // 记录消息保存日志
            log.info("Chat消息保存开始 - 发送人: {}, 会话ID: {}, 消息ID: {}, 消息来源: {}, 消息类型: {}",
                    misId, agentMessageDTO.getSessionId(), agentMessageDTO.getMsgId(),
                    agentMessageDTO.getSource(), agentMessageDTO.getMessageType());

            // 保存原始消息
            TCheckResponse saveResult = TAgentMessageService.saveSingleMessage(agentMessageDTO);
            if (saveResult != null && saveResult.getCode() == 200) {
                log.info("Chat消息保存成功 - 发送人: {}, 会话ID: {}, 消息ID: {}",
                        misId, agentMessageDTO.getSessionId(), agentMessageDTO.getMsgId());
            } else {
                log.error("Chat消息保存失败 - 发送人: {}, 会话ID: {}, 消息ID: {}, 错误信息: {}",
                        misId, agentMessageDTO.getSessionId(), agentMessageDTO.getMsgId(),
                        saveResult != null ? saveResult.getMessage() : "未知错误");
            }
            stopWatch.stop();
            stopWatch.start("http请求");
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("sessionId", agentMessageDTO.getSessionId());
            paramMap.put("attachment", agentMessageDTO.getAttachment());
            paramMap.put("attachmentType", agentMessageDTO.getAttachmentType());
            paramMap.put("content", agentMessageDTO.getContent());
            paramMap.put("moduleName", moduleName);
            paramMap.put("agent_code", agentCode);
            paramMap.put("mis_id", owner);

            // 记录HTTP请求日志
            log.info("Chat消息HTTP请求开始 - 发送人: {}, 会话ID: {}, 目标URL: {}, Agent代码: {}, 模块名: {}",
                    misId, agentMessageDTO.getSessionId(), actualChatUrl, agentCode, moduleName);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(paramMap, headers);
            ResponseEntity<String> resp = restTemplate.postForEntity(actualChatUrl, request, String.class);

            // 记录HTTP响应日志
            log.info("Chat消息HTTP请求完成 - 发送人: {}, 会话ID: {}, 响应状态: {}, 响应体长度: {}",
                    misId, agentMessageDTO.getSessionId(), resp.getStatusCode(),
                    resp.getBody() != null ? resp.getBody().length() : 0);

            // Chat请求成功后，更新IP映射关系（与SSE建联逻辑一致）
            if (resp.getStatusCode().is2xxSuccessful()) {
                //更新状态为运行中
                sessionServiceImpl.updateSessionStatus(agentMessageDTO.getSessionId(), SessionStatusEnum.RUNNING.getCode());
                if (status == SessionStatusEnum.PENDING.getCode()) {
                    log.info("PENDING状态唤醒成功，状态流转为RUNNING状态 - 发送人: {}, 会话ID: {}, 会话所有者: {}, Agent代码: {}, 原始状态：{}",
                            misId, agentMessageDTO.getSessionId(), owner, agentCode,SessionStatusEnum.getDescByCode(status));
                }else {
                    log.info("状态流转为RUNNING状态成功 - 发送人: {}, 会话ID: {}, 会话所有者: {}, Agent代码: {}, 原始状态：{}",
                            misId, agentMessageDTO.getSessionId(), owner, agentCode,SessionStatusEnum.getDescByCode(status));
                }
                try {
                    sessionServiceImpl.updateSessionIpMapping(agentMessageDTO.getSessionId(), targetIp);
                    log.info("Chat请求成功，已更新IP映射: sessionId={}, targetIp={}",
                            agentMessageDTO.getSessionId(), targetIp);
                } catch (Exception e) {
                    log.warn("更新IP映射失败，但不影响Chat响应: sessionId={}, targetIp={}",
                            agentMessageDTO.getSessionId(), targetIp, e);
                }
            }

            String responseBody = resp.getBody();
            Gson gson = new Gson();
            Type type = new TypeToken<Map<String, Object>>(){}.getType();
            Map<String, Object> resultMap = gson.fromJson(responseBody, type);
            Map<String, String> stringResultMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                stringResultMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
            stopWatch.stop();

            // 记录chat消息处理成功日志
            log.info("Chat消息处理成功 - 发送人: {}, 会话ID: {}, 处理耗时: {}",
                    misId, agentMessageDTO.getSessionId(), stopWatch.getTotalTimeMillis());
            log.info(stopWatch.prettyPrint());
            return TChatResponse.success(stringResultMap);
        } catch (Exception e) {
            // 记录chat消息处理异常日志
            log.error("Chat消息处理异常 - 发送人: {}, 会话ID: {}, 消息内容: {}, 异常信息: {}",
                    misId, agentMessageDTO.getSessionId(),
                    agentMessageDTO.getContent() != null ?
                        (agentMessageDTO.getContent().length() > 100 ?
                            agentMessageDTO.getContent().substring(0, 100) + "..." :
                            agentMessageDTO.getContent()) : "null",
                    e.getMessage(), e);
            return TChatResponse.fail(500, e.getMessage());
        }
    }

    @Override
    public TChatResponse trans2agent(String source, String sessionId, Map<String, String> content) {
        if (StringUtils.isBlank(source)) {
            source = "third_callback";
        }
        AgentMessageDTO agentMessageDTO = new AgentMessageDTO();
        agentMessageDTO.setSessionId(sessionId);
        agentMessageDTO.setSource(source);
        agentMessageDTO.setContent(new Gson().toJson(content));
        return chat("third_callback", agentMessageDTO);
    }
}
