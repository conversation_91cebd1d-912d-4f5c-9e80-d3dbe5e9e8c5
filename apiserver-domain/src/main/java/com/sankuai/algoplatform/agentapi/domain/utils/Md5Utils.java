package com.sankuai.algoplatform.agentapi.domain.utils;

import com.dianping.cat.Cat;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2024年03月27日 6:45 下午
 */
public class Md5Utils {

    public static String generateMd5(String input) throws NoSuchAlgorithmException {
        // 创建MD5消息摘要对象
        MessageDigest md = MessageDigest.getInstance("MD5");

        // 将输入字符串转换为字节数组
        byte[] inputBytes = input.getBytes();

        // 计算MD5哈希值
        byte[] hashBytes = md.digest(inputBytes);

        // 将字节数组转换为十六进制字符串
        StringBuilder sb = new StringBuilder();
        for (byte b : hashBytes) {
            sb.append(String.format("%02x", b));
        }

        // 返回生成的MD5哈希值
        return sb.toString();
    }
}
