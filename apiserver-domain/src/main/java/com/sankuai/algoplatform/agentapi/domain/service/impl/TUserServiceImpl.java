package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.agentapi.client.dto.UserInfo;
import com.sankuai.algoplatform.agentapi.client.enums.ResponseCodeEnum;
import com.sankuai.algoplatform.agentapi.client.response.user.TUserInfoResponse;
import com.sankuai.algoplatform.agentapi.client.service.TUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025年06月18日 16:42
 */
@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TUserServiceImpl implements TUserService {
    @Override
    public TUserInfoResponse getUserInfo(String misId) {
        TUserInfoResponse tUserInfoResponse = new TUserInfoResponse();
        UserInfo userInfo = new UserInfo();
        userInfo.setMisId(misId);
        tUserInfoResponse.setData(userInfo);
        tUserInfoResponse.setCode(ResponseCodeEnum.SUCCESS.getCode());
        return tUserInfoResponse;
    }
}
