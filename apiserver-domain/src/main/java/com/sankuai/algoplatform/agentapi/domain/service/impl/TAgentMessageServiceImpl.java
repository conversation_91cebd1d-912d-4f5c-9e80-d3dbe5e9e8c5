package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.dto.AgentOriginalMessageDTO;
import com.sankuai.algoplatform.agentapi.client.dto.PageInfo;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.TStringResponse;
import com.sankuai.algoplatform.agentapi.client.response.message.TMessagesResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentMessageService;
import com.sankuai.algoplatform.agentapi.domain.repository.AgentMessageRepository;
import com.sankuai.algoplatform.agentapi.domain.repository.AgentNodeFlowRepository;
import com.sankuai.algoplatform.agentapi.domain.repository.AgentOriginalMessageRepository;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * AgentMessageService 接口实现类
 */
@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TAgentMessageServiceImpl implements TAgentMessageService {

    @Autowired
    private AgentMessageRepository agentMessageRepository;

    @Resource
    private AgentNodeFlowRepository agentNodeFlowRepository;

    @Autowired
    private AgentOriginalMessageRepository agentOriginalMessageRepository;

    //"liuchengyanzheng": "配置流程结果一致性验证，返回加工字段和字段一致性结果"
    private static final String resultFlag = "liuchengyanzheng";

    // 定义时间格式化器
    private SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public TMessagesResponse getSessionHistory(String misId, String sessionId, String startMsgId, Integer pageSize) {
        try {
            List<AgentMessage> messages;
            PageInfo pageInfo = new PageInfo();
            if (StringUtils.isEmpty(startMsgId)) {
                pageInfo.setHasNext(!agentMessageRepository.isLastPageByStartMsgId(sessionId, pageSize));
                messages = agentMessageRepository.pageQueryByStartMsgId(sessionId, pageSize);
            } else {
                Long startId = agentMessageRepository.getStartId(sessionId, startMsgId);
                pageInfo.setHasNext(!agentMessageRepository.isLastPageByStartId(sessionId, startId, pageSize));
                messages = agentMessageRepository.pageQueryByStartId(sessionId, startId, pageSize);
            }
            if (!pageInfo.getHasNext()) {
                pageInfo.setPageSize(messages.size());
            }
            List<AgentMessageDTO> agentMessageDTOS = convertToDTOList(messages);
            TMessagesResponse tMessagesResponse = new TMessagesResponse();
            tMessagesResponse.setCode(200);
            tMessagesResponse.setData(agentMessageDTOS);
            tMessagesResponse.setPageInfo(pageInfo);
            return tMessagesResponse;
        } catch (Exception e) {
            TMessagesResponse tMessagesResponse = new TMessagesResponse();
            tMessagesResponse.setCode(500);
            tMessagesResponse.setMessage("获取会话历史记录失败: " + e.getMessage());
            log.error("获取会话历史记录失败: ", e);
            return tMessagesResponse;
        }
    }

    @Override
    public TCheckResponse saveSingleMessage(AgentMessageDTO agentMessageDTO) {
        try {
            AgentMessage message = convertToModel(agentMessageDTO);
            if (StringUtils.isEmpty(message.getMsgId())) {
                String uuidString = UUID.randomUUID().toString();
                message.setMsgId(uuidString);
            }
            if (StringUtils.isEmpty(message.getMessageType())) {
                message.setMessageType("文本消息");
            }
            if (StringUtils.isEmpty(message.getSource())) {
                message.setSource("user");
            }
            agentMessageRepository.insertOrUpdate(message);
            log.info("保存消息成功: sessionId={}, msgId={}", message.getSessionId(), message.getMsgId());
            return TCheckResponse.success(true);
        } catch (Exception e) {
            log.error("保存消息失败: sessionId={}, msgId={}",
                    agentMessageDTO != null ? agentMessageDTO.getSessionId() : "null",
                    agentMessageDTO != null ? agentMessageDTO.getMsgId() : "null", e);
            return TCheckResponse.fail("保存消息失败: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse saveMessages(List<AgentMessageDTO> messageDTOS) {
        try {
            if (messageDTOS == null || messageDTOS.isEmpty()) {
                return TCheckResponse.success(true);
            }
            List<AgentMessage> messages = messageDTOS.stream()
                    .map(this::convertToModel)
                    .collect(Collectors.toList());
            agentMessageRepository.batchInsertOrUpdate(messages);
            log.info("批量保存消息成功: 数量={}", messages.size());
            return TCheckResponse.success(true);
        } catch (Exception e) {
            log.error("批量保存消息失败: 数量={}", messageDTOS != null ? messageDTOS.size() : 0, e);
            return TCheckResponse.fail("批量保存消息失败: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse saveSingleOriginalMessage(AgentOriginalMessageDTO messageDTO) {
        try {
            AgentOriginalMessageWithBLOBs message = convertToOriginalModel(messageDTO);
            agentOriginalMessageRepository.insertOrUpdate(message);
            log.info("保存原始消息成功: sessionId={}, msgId={}", message.getSessionId(), message.getMsgId());
            return TCheckResponse.success(true);
        } catch (Exception e) {
            log.error("保存原始消息失败: sessionId={}, msgId={}",
                    messageDTO != null ? messageDTO.getSessionId() : "null",
                    messageDTO != null ? messageDTO.getMsgId() : "null", e);
            return TCheckResponse.fail("保存原始消息失败: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse saveOriginalMessages(List<AgentOriginalMessageDTO> messageDTOS) {
        try {
            if (messageDTOS == null || messageDTOS.isEmpty()) {
                return TCheckResponse.success(true);
            }
            List<AgentOriginalMessageWithBLOBs> messages = messageDTOS.stream()
                    .map(this::convertToOriginalModel)
                    .collect(Collectors.toList());
            agentOriginalMessageRepository.batchInsertOrUpdate(messages);
            log.info("批量保存原始消息成功: 数量={}", messages.size());
            return TCheckResponse.success(true);
        } catch (Exception e) {
            log.error("批量保存原始消息失败: 数量={}", messageDTOS != null ? messageDTOS.size() : 0, e);
            return TCheckResponse.fail("批量保存原始消息失败: " + e.getMessage());
        }
    }

    /**
     * 将消息领域模型转换为DTO
     *
     * @param message 领域模型
     * @return DTO
     */
    private AgentMessageDTO convertToDTO(AgentMessage message) {
        if (message == null) {
            return null;
        }

        AgentMessageDTO dto = new AgentMessageDTO();
        BeanUtils.copyProperties(message, dto);
        // Date格式转化为String类型
        if (message.getAddTime() != null) {
            dto.setAddTime(DATE_FORMAT.format(message.getAddTime()));
        }
        return dto;
    }

    /**
     * 将消息DTO转换为领域模型
     *
     * @param dto DTO
     * @return 领域模型
     */
    private AgentMessage convertToModel(AgentMessageDTO dto) {
        if (dto == null) {
            return null;
        }
        AgentMessage model = new AgentMessage();
        BeanUtils.copyProperties(dto, model);
        return model;
    }

    /**
     * 将原始消息DTO转换为领域模型
     *
     * @param dto DTO
     * @return 领域模型
     */
    private AgentOriginalMessageWithBLOBs convertToOriginalModel(AgentOriginalMessageDTO dto) {
        if (dto == null) {
            return null;
        }
        AgentOriginalMessageWithBLOBs model = new AgentOriginalMessageWithBLOBs();
        BeanUtils.copyProperties(dto, model);
        return model;
    }

    /**
     * 将消息领域模型列表转换为DTO列表
     *
     * @param messages 领域模型列表
     * @return DTO列表
     */
    private List<AgentMessageDTO> convertToDTOList(List<AgentMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            return new ArrayList<>();
        }
        return messages.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public TStringResponse getAnalysisData(String sessionId) {
        try {
            TStringResponse response = new TStringResponse();
            response.setCode(200);
            List<AgentNodeFlow> agentNodeFlows = agentNodeFlowRepository.listBySessionIdAndCurrentNodeNameDESC(sessionId, resultFlag);
            if (CollectionUtils.isEmpty(agentNodeFlows)) {
                return response;
            }
            AgentNodeFlow agentNodeFlow = agentNodeFlows.get(0);
            AgentMessage byMsgId = agentMessageRepository.getByMsgId(agentNodeFlow.getMsgId());
            String content = byMsgId.getContent();
            if (StringUtils.isEmpty(content)) {
                return response;
            }
            JSONObject jsonObject = JSON.parseObject(content);
            JSONArray innoContent = jsonObject.getJSONArray("content");
            if (CollectionUtils.isEmpty(innoContent) || innoContent.size() < 2) {
                return response;
            }
            Object innoContent1 = innoContent.get(1);
            if (innoContent1 == null) {
                return response;
            }
            JSONObject innoContent1JSONObject = JSON.parseObject(innoContent1.toString());
            String node_log = innoContent1JSONObject.getString("node_log");
            response.setData(node_log);
            return response;
        } catch (Exception e) {
            TStringResponse response = new TStringResponse();
            response.setCode(500);
            response.setMessage("获取解析数据失败: " + e.getMessage());
            return response;
        }
    }
}