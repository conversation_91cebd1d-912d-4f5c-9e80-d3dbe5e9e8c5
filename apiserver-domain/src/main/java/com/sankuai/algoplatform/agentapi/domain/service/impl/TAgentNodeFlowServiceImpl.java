package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.agentapi.client.dto.AgentNodeFlowDTO;
import com.sankuai.algoplatform.agentapi.client.response.Response;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.node.TNodesResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentNodeFlowService;
import com.sankuai.algoplatform.agentapi.domain.repository.AgentNodeFlowRepository;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Agent节点流转服务实现类
 */
@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TAgentNodeFlowServiceImpl implements TAgentNodeFlowService {

    @Resource
    private AgentNodeFlowRepository agentNodeFlowRepository;

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public TCheckResponse saveSingleNodeFlow(AgentNodeFlowDTO nodeFlowDTO) {
        try {
            if (nodeFlowDTO == null) {
                return TCheckResponse.fail("节点流转数据不能为空");
            }

            // 转换DTO为PO
            AgentNodeFlow agentNodeFlow = convertToAgentNodeFlow(nodeFlowDTO);
            
            // 设置默认添加时间
            if (agentNodeFlow.getAddTime() == null) {
                agentNodeFlow.setAddTime(new Date());
            }
            
            // 保存到数据库
            int result = agentNodeFlowRepository.insertOrUpdate(agentNodeFlow);

            if (result > 0) {
                log.info("保存节点流转记录成功: sessionId={}, msgId={}, currentNode={}",
                        agentNodeFlow.getSessionId(), agentNodeFlow.getMsgId(), agentNodeFlow.getCurrentNodeName());
                return TCheckResponse.success(true);
            } else {
                log.error("保存节点流转记录失败: sessionId={}, msgId={}, currentNode={}",
                        agentNodeFlow.getSessionId(), agentNodeFlow.getMsgId(), agentNodeFlow.getCurrentNodeName());
                return TCheckResponse.fail("保存节点流转记录失败");
            }
        } catch (Exception e) {
            log.error("保存节点流转记录异常: sessionId={}, msgId={}",
                    nodeFlowDTO != null ? nodeFlowDTO.getSessionId() : "null",
                    nodeFlowDTO != null ? nodeFlowDTO.getMsgId() : "null", e);
            return TCheckResponse.fail("保存节点流转记录异常: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse batchSaveNodeFlow(List<AgentNodeFlowDTO> nodeFlowDTOList) {
        try {
            if (CollectionUtils.isEmpty(nodeFlowDTOList)) {
                return TCheckResponse.fail("节点流转数据列表不能为空");
            }

            // 转换DTO列表为PO列表
            List<AgentNodeFlow> agentNodeFlows = nodeFlowDTOList.stream()
                    .map(this::convertToAgentNodeFlow)
                    .collect(Collectors.toList());
            
            // 批量保存到数据库
            int result = agentNodeFlowRepository.batchInsertOrUpdate(agentNodeFlows);

            if (result > 0) {
                log.info("批量保存节点流转记录成功: 数量={}", agentNodeFlows.size());
                return TCheckResponse.success(true);
            } else {
                log.error("批量保存节点流转记录失败: 数量={}", agentNodeFlows.size());
                return TCheckResponse.fail("批量保存节点流转记录失败");
            }
        } catch (Exception e) {
            log.error("批量保存节点流转记录异常: 数量={}", nodeFlowDTOList != null ? nodeFlowDTOList.size() : 0, e);
            return TCheckResponse.fail("批量保存节点流转记录异常: " + e.getMessage());
        }
    }

    @Override
    public TNodesResponse listNodeFlowBySessionId(String sessionId) {
        try {
            if (sessionId == null || sessionId.isEmpty()) {
                return TNodesResponse.fail(500,"会话ID不能为空");
            }

            // 查询数据库
            List<AgentNodeFlow> agentNodeFlows = agentNodeFlowRepository.listBySessionId(sessionId);
            
            // 转换PO列表为DTO列表
            List<AgentNodeFlowDTO> nodeFlowDTOList = convertToAgentNodeFlowDTOList(agentNodeFlows);
            
            return TNodesResponse.success(nodeFlowDTOList);
        } catch (Exception e) {
            log.error("查询会话节点流转记录异常: sessionId={}", sessionId, e);
            return TNodesResponse.fail(500,"查询会话节点流转记录异常: " + e.getMessage());
        }
    }

    @Override
    public Response<List<AgentNodeFlowDTO>> listNodeFlowByMsgId(String msgId) {
        try {
            if (msgId == null || msgId.isEmpty()) {
                return Response.fail("消息ID不能为空");
            }

            // 查询数据库
            List<AgentNodeFlow> agentNodeFlows = agentNodeFlowRepository.listByMsgId(msgId);
            
            // 转换PO列表为DTO列表
            List<AgentNodeFlowDTO> nodeFlowDTOList = convertToAgentNodeFlowDTOList(agentNodeFlows);
            
            return Response.success(nodeFlowDTOList);
        } catch (Exception e) {
            log.error("查询消息节点流转记录异常: msgId={}", msgId, e);
            return Response.fail("查询消息节点流转记录异常: " + e.getMessage());
        }
    }

    @Override
    public TNodesResponse listNodeFlowBySessionIdAndMsgId(String sessionId, String msgId) {
        try {
            if (sessionId == null || sessionId.isEmpty()) {
                return TNodesResponse.fail(500, "会话ID不能为空");
            }
            if (msgId == null || msgId.isEmpty()) {
                return TNodesResponse.fail(500, "消息ID不能为空");
            }

            // 查询数据库
            List<AgentNodeFlow> agentNodeFlows = agentNodeFlowRepository.listBySessionIdAndMsgId(sessionId, msgId);
            
            // 转换PO列表为DTO列表
            List<AgentNodeFlowDTO> nodeFlowDTOList = convertToAgentNodeFlowDTOList(agentNodeFlows);
            
            return TNodesResponse.success(nodeFlowDTOList);
        } catch (Exception e) {
            log.error("查询会话和消息节点流转记录异常: sessionId={}, msgId={}", sessionId, msgId, e);
            return TNodesResponse.fail(500, "查询会话和消息节点流转记录异常: " + e.getMessage());
        }
    }

    @Override
    public TNodesResponse listNodeFlowByCurrentNodeName(String sessionId, String currentNodeName) {
        try {
            if (sessionId == null || sessionId.isEmpty()) {
                return TNodesResponse.fail(500,"会话ID不能为空");
            }
            if (currentNodeName == null || currentNodeName.isEmpty()) {
                return TNodesResponse.fail(500,"当前节点名称不能为空");
            }

            // 查询数据库
            List<AgentNodeFlow> agentNodeFlows = agentNodeFlowRepository.listBySessionIdAndCurrentNodeName(sessionId, currentNodeName);

            // 转换PO列表为DTO列表
            List<AgentNodeFlowDTO> nodeFlowDTOList = convertToAgentNodeFlowDTOList(agentNodeFlows);

            return TNodesResponse.success(nodeFlowDTOList);
        } catch (Exception e) {
            log.error("查询会话和当前节点流转记录异常: sessionId={}, currentNodeName={}", sessionId, currentNodeName, e);
            return TNodesResponse.fail(500,"查询会话和当前节点流转记录异常: " + e.getMessage());
        }
    }

    /**
     * 将DTO转换为PO
     *
     * @param nodeFlowDTO 节点流转DTO
     * @return 节点流转PO
     */
    private AgentNodeFlow convertToAgentNodeFlow(AgentNodeFlowDTO nodeFlowDTO) {
        if (nodeFlowDTO == null) {
            return null;
        }
        
        AgentNodeFlow agentNodeFlow = new AgentNodeFlow();
        BeanUtils.copyProperties(nodeFlowDTO, agentNodeFlow);
        return agentNodeFlow;
    }

    /**
     * 将PO列表转换为DTO列表
     *
     * @param agentNodeFlows 节点流转PO列表
     * @return 节点流转DTO列表
     */
    private List<AgentNodeFlowDTO> convertToAgentNodeFlowDTOList(List<AgentNodeFlow> agentNodeFlows) {
        if (CollectionUtils.isEmpty(agentNodeFlows)) {
            return new ArrayList<>();
        }
        
        return agentNodeFlows.stream()
                .map(this::convertToAgentNodeFlowDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将PO转换为DTO
     *
     * @param agentNodeFlow 节点流转PO
     * @return 节点流转DTO
     */
    private AgentNodeFlowDTO convertToAgentNodeFlowDTO(AgentNodeFlow agentNodeFlow) {
        if (agentNodeFlow == null) {
            return null;
        }
        
        AgentNodeFlowDTO nodeFlowDTO = new AgentNodeFlowDTO();
        BeanUtils.copyProperties(agentNodeFlow, nodeFlowDTO);

        // 特殊处理Date类型转String
        if (agentNodeFlow.getAddTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
            nodeFlowDTO.setAddTime(sdf.format(agentNodeFlow.getAddTime()));
        }
        return nodeFlowDTO;
    }
}
