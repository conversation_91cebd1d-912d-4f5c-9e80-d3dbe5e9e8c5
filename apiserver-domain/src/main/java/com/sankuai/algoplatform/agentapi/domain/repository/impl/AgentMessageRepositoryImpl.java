package com.sankuai.algoplatform.agentapi.domain.repository.impl;

import com.dianping.cat.util.StringUtils;
import com.sankuai.algoplatform.agentapi.domain.repository.AgentMessageRepository;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentMessageExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentMessageMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * AgentMessageDAO 接口实现类
 */
@Repository
public class AgentMessageRepositoryImpl implements AgentMessageRepository {

    @Resource
    private AgentMessageMapper agentMessageMapper;

    @Override
    public AgentMessage getByMsgId(String msgId) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria().andMsgIdEqualTo(msgId);
        List<AgentMessage> messages = agentMessageMapper.selectByExampleWithBLOBs(example);
        return messages.isEmpty() ? null : messages.get(0);
    }

    @Override
    public List<AgentMessage> listBySessionId(String sessionId) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        return agentMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentMessage> listBySessionIdAndMessageType(String sessionId, String messageType) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andMessageTypeEqualTo(messageType);
        return agentMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentMessage> listBySessionIdAndSource(String sessionId, String source) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andSourceEqualTo(source);
        return agentMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public int insert(AgentMessage agentMessage) {
        return agentMessageMapper.insert(agentMessage);
    }

    @Override
    public int batchInsert(List<AgentMessage> agentMessages) {
        return agentMessageMapper.batchInsert(agentMessages);
    }

    @Override
    public int insertOrUpdate(AgentMessage agentMessage) {
        return agentMessageMapper.insertOrUpdate(agentMessage);
    }

    @Override
    public int batchInsertOrUpdate(List<AgentMessage> agentMessages) {
        return agentMessageMapper.batchInsertOrUpdate(agentMessages);
    }

    @Override
    public int update(AgentMessage agentMessage) {
        return agentMessageMapper.updateByPrimaryKeySelective(agentMessage);
    }

    @Override
    public int deleteByMsgId(String msgId) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria().andMsgIdEqualTo(msgId);
        return agentMessageMapper.deleteByExample(example);
    }

    @Override
    public int deleteBySessionId(String sessionId) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        return agentMessageMapper.deleteByExample(example);
    }

    @Override
    public List<AgentMessage> pageQueryBySessionId(String sessionId, int offset, int limit) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        example.setOffset(offset);
        example.setRows(limit);
        return agentMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public long countBySessionId(String sessionId) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        return agentMessageMapper.countByExample(example);
    }

    @Override
    public boolean isLastPageByStartId(String sessionId, Long startMsgId,int pageSize) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andIdLessThan(startMsgId);
        return  agentMessageMapper.countByExample(example)<= pageSize;
    }

    @Override
    public boolean isLastPageByStartMsgId(String sessionId, int pageSize) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId);
        return agentMessageMapper.countByExample(example)<= pageSize;
    }

    @Override
    public List<AgentMessage> pageQueryByStartId(String sessionId, Long startId, int pageSize){
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andIdLessThan(startId);
        example.setRows(pageSize);
        example.setOrderByClause("id DESC");
        return agentMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentMessage> pageQueryByStartMsgId(String sessionId, int pageSize) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId);
        example.setRows(pageSize);
        example.setOrderByClause("id DESC");
        return agentMessageMapper.selectByExampleWithBLOBs(example);
    }


    @Override
    public Long getStartId(String sessionId, String startMsgId) {
        AgentMessageExample example = new AgentMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andMsgIdEqualTo(startMsgId);
        return agentMessageMapper.selectByExample(example).get(0).getId();
    }
}