package com.sankuai.algoplatform.agentapi.domain.repository.impl;

import com.sankuai.algoplatform.agentapi.domain.repository.AgentOriginalMessageRepository;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentOriginalMessageExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentOriginalMessageMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessage;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentOriginalMessageWithBLOBs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * AgentOriginalMessageDAO 接口实现类
 */
@Repository
public class AgentOriginalMessageRepositoryImpl implements AgentOriginalMessageRepository {

    @Resource
    private AgentOriginalMessageMapper agentOriginalMessageMapper;

    @Override
    public AgentOriginalMessage getByMsgId(String msgId) {
        return agentOriginalMessageMapper.selectByPrimaryKey(msgId);
    }

    @Override
    public List<AgentOriginalMessage> listBySessionId(String sessionId) {
        AgentOriginalMessageExample example = new AgentOriginalMessageExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        return agentOriginalMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentOriginalMessage> listBySessionIdAndSource(String sessionId, String source) {
        AgentOriginalMessageExample example = new AgentOriginalMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andSourceEqualTo(source);
        return agentOriginalMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<AgentOriginalMessage> listBySessionIdAndType(String sessionId, String type) {
        AgentOriginalMessageExample example = new AgentOriginalMessageExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andTypeEqualTo(type);
        return agentOriginalMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public int insert(AgentOriginalMessageWithBLOBs agentOriginalMessage) {
        return agentOriginalMessageMapper.insert(agentOriginalMessage);
    }

    @Override
    public int batchInsert(List<AgentOriginalMessageWithBLOBs> agentOriginalMessages) {
        return agentOriginalMessageMapper.batchInsert(agentOriginalMessages);
    }

    @Override
    public int insertOrUpdate(AgentOriginalMessageWithBLOBs agentOriginalMessage) {
        return agentOriginalMessageMapper.insertOrUpdate(agentOriginalMessage);
    }

    @Override
    public int batchInsertOrUpdate(List<AgentOriginalMessageWithBLOBs> agentOriginalMessages) {
        return agentOriginalMessageMapper.batchInsertOrUpdate(agentOriginalMessages);
    }

    @Override
    public int update(AgentOriginalMessage agentOriginalMessage) {
        return agentOriginalMessageMapper.updateByPrimaryKeySelective(agentOriginalMessage);
    }

    @Override
    public int deleteByMsgId(String msgId) {
        return agentOriginalMessageMapper.deleteByPrimaryKey(msgId);
    }

    @Override
    public int deleteBySessionId(String sessionId) {
        AgentOriginalMessageExample example = new AgentOriginalMessageExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        return agentOriginalMessageMapper.deleteByExample(example);
    }

    @Override
    public List<AgentOriginalMessage> pageQueryBySessionId(String sessionId, int offset, int limit) {
        AgentOriginalMessageExample example = new AgentOriginalMessageExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        example.setOffset(offset);
        example.setRows(limit);
        return agentOriginalMessageMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public long countBySessionId(String sessionId) {
        AgentOriginalMessageExample example = new AgentOriginalMessageExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        return agentOriginalMessageMapper.countByExample(example);
    }
}