package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.agentapi.client.request.AllocateTaskForAgentRequest;
import com.sankuai.algoplatform.agentapi.client.request.SendMisCardRequest;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import com.sankuai.algoplatform.agentapi.client.response.TMapResponse;
import com.sankuai.algoplatform.agentapi.client.response.TStringResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentSessionService;
import com.sankuai.algoplatform.agentapi.client.service.TAgentTaskService;
import com.sankuai.algoplatform.agentapi.client.service.TOpenCardService;
import com.sankuai.inf.octo.mns.model.HostEnv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Agent任务服务实现类
 * <AUTHOR>
 * @date 2025年07月07日
 */
@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TAgentTaskServiceImpl implements TAgentTaskService {

    @Autowired
    private TAgentSessionService agentSessionService;

    @Autowired
    private TOpenCardService openCardService;

    @MdpConfig("agent_2_rd:{}")
    private HashMap<String, String> agent2rd;

    // 大象消息模板ID配置 - 线下环境
    @Value("${agent.task.template.offline.text.id:22794}")
    private Long offlineTextTemplateId; // 线下仅文本模板

    @Value("${agent.task.template.offline.confirm.id:22795}")
    private Long offlineConfirmTemplateId; // 线下文本 + 确认模板

    @Value("${agent.task.template.offline.input.id:22796}")
    private Long offlineInputTemplateId; // 线下文本 + 输入 + 确认模板

    // 大象消息模板ID配置 - 线上环境
    @Value("${agent.task.template.online.text.id:21145}")
    private Long onlineTextTemplateId; // 线上仅文本模板

    @Value("${agent.task.template.online.confirm.id:21144}")
    private Long onlineConfirmTemplateId; // 线上文本 + 确认模板

    @Value("${agent.task.template.online.input.id:21143}")
    private Long onlineInputTemplateId; // 线上文本 + 输入 + 确认模板

    // Agent页面基础URL配置
    @Value("${agent.page.base.url:https://bml.nibdata.test.sankuai.com/agent}")
    private String agentPageBaseUrl;

    @Override
    public TMapResponse allocateTaskForAgent(AllocateTaskForAgentRequest request) {
        try {
            // 从请求对象中提取参数
            String parentSessionId = request.getParentSessionId();
            String taskDesc = request.getTaskDesc();
            String agentCode = request.getAgentCode();
            String misId = request.getMisId();
            String source = request.getSource();

            log.info("开始为Agent分配任务: parentSessionId={}, taskDesc={}, agentCode={}, misId={}, source={}",
                    parentSessionId, taskDesc, agentCode, misId, source);

            // 1. misId 和 source 非必填，source 默认值为 "bml_agent"
            if (StringUtils.isBlank(source)) {
                source = "bml_agent";
                log.info("source为空，使用默认值: {}", source);
            }

            // 2. 根据 agentCode 查询 agent2rd 获取对应的 rdMis，如果为空则使用传递的 misId
            String rdMisId = getRdMisByAgentCode(agentCode);
            if (StringUtils.isBlank(rdMisId)) {
                log.info("未查询到当前 agentCode 对应 rdMis，给当前会话创建人发送，当前agentCode: {}，misId: {}", agentCode, misId);
                rdMisId = misId;
            }
            log.info("根据agentCode查询到对应的rdMis: agentCode={}, rdMis={}", agentCode, rdMisId);

            // 3. 根据 agentCode 和 rdMisId 来新建会话，调用 createSession 接口
            TStringResponse createSessionResponse = agentSessionService.createSession(rdMisId, agentCode);
            if (createSessionResponse == null || createSessionResponse.getCode() != 200) {
                String errorMsg = "创建会话失败: " + (createSessionResponse != null ? createSessionResponse.getMessage() : "返回为空");
                log.error(errorMsg);
                return TMapResponse.fail(500, errorMsg);
            }

            String sessionId = createSessionResponse.getData();
            log.info("创建会话成功: sessionId={}", sessionId);

            // 4. 根据 taskDesc 、 createSession 获取到的 sessionId 来拼接一个跳转的 url
            String jumpUrl = buildAgentTaskUrl(sessionId, agentCode, taskDesc);
            log.info("拼接跳转URL成功: {}", jumpUrl);

            // 6. 把 4 拼接的 url，调用大象消息发送接口进行发送
            String sendRd = "";
            try {
                // 根据agentCode发送预告消息
                sendPreNotificationMessage(misId, agentCode);
                sendRd = sendElephantMessage(misId, rdMisId, taskDesc, parentSessionId, jumpUrl, agentCode, request.getConfirmCallback());
                log.info("大象消息发送成功: sendRd={}", sendRd);
            } catch (Exception e) {
                log.error("大象消息发送失败: {}", e.getMessage(), e);
                // 消息发送失败不影响整体流程，继续执行
                sendRd = "大象消息发送失败: " + e.getMessage();
                return TMapResponse.fail(500, sendRd);
            }

            // 7. 返回结果，把拼接的 url、发送的 rd、createSession 返回的 sessionId 返回
            Map<String, String> resultData = new HashMap<>();
            resultData.put("sessionId", sessionId);
            resultData.put("isLongTermTask", "是");

            log.info("Agent任务分配成功: sessionId={}, jumpUrl={}, sendRd={}", sessionId, jumpUrl, sendRd);
            return TMapResponse.success(resultData);

        } catch (Exception e) {
            // 8. 如果执行有错误，把错误信息返回
            String errorMsg = "分配Agent任务失败: " + e.getMessage();
            log.error(errorMsg, e);
            return TMapResponse.fail(500, errorMsg);
        }
    }

    /**
     * 发送大象消息
     *
     * @param sendMisId 发送用户ID
     * @param rdMisId rd用户ID
     * @param taskDesc 任务描述
     * @param sessionId 会话ID
     * @param jumpUrl 跳转URL
     * @param agentCode Agent代码
     * @param confirmCallback 确认回调文本
     * @return 发送结果描述
     */
    private String sendElephantMessage(String sendMisId, String rdMisId, String taskDesc, String sessionId, String jumpUrl, String agentCode, String confirmCallback) {
        try {
            // 根据agentCode构造不同的消息内容、变量值和模板ID
            Map<String, String> variableValue = new HashMap<>();
            String messageText;
            String abstractText;
            Long templateId;

            if ("parser_agent".equals(agentCode)) {
                // parser_agent: 仅文本消息
                String linkText = String.format("[链接|%s]", jumpUrl);
                messageText = String.format("[%s]已启动解析加工任务，点击%s接管任务", sendMisId, linkText);
                abstractText = String.format("[%s]已启动解析加工任务", sendMisId);
                variableValue.put("txt", messageText);
                templateId = getTextTemplateId(); // 根据环境选择仅文本模板

            } else if ("match_agent".equals(agentCode)) {
                // match_agent: 文本 + 输入 + 确认
                String linkText = String.format("[链接|%s]", jumpUrl);
                messageText = String.format("[%s]已启动匹配Agent任务：%s，点击%s接管任务 , 任务完成后输入匹配效果报告链接，并点击【确认】", sendMisId, taskDesc, linkText);
                abstractText = String.format("[%s]已启动匹配Agent任务", sendMisId);
                variableValue.put("txt", messageText);
                // 只有22628模板才使用confirmCallback
                if (StringUtils.isNotBlank(confirmCallback)) {
                    variableValue.put("confirmCallback", confirmCallback);
                } else {
                    variableValue.put("confirmCallback", "任务已完成"); // 默认值
                }
                templateId = getInputTemplateId(); // 根据环境选择文本 + 输入 + 确认模板

            } else {
                // 默认消息模板
                messageText = String.format("[%s]已启动Agent任务：%s", sendMisId, taskDesc);
                if (StringUtils.isNotBlank(jumpUrl)) {
                    String linkText = String.format("[链接|%s]", jumpUrl);
                    messageText += "，点击" + linkText + "查看详情";
                }
                abstractText = String.format("[%s]已启动Agent任务", sendMisId);
                variableValue.put("txt", messageText);
                templateId = getTextTemplateId(); // 根据环境选择仅文本模板
            }

            // 发送消息
            SendMisCardRequest sendRequest = new SendMisCardRequest();
            sendRequest.setTemplateId(templateId);
            sendRequest.setMisList(Arrays.asList(rdMisId)); // 使用rdMisId作为实际接收方
            sendRequest.setAbstractText(abstractText);
            sendRequest.setSessionId(sessionId);
            sendRequest.setVariableValue(variableValue);

            TBaseReponse response = openCardService.sendMisCard(sendRequest);

            if (response != null && response.getCode() == 200) {
                log.info("大象消息发送成功: sendMisId={}, rdMisId={}, sessionId={}, templateId={}, agentCode={}", sendMisId, rdMisId, sessionId, templateId, agentCode);
                return "发送成功";
            } else {
                String errorMsg = response != null ? response.getMessage() : "返回为空";
                log.error("大象消息发送失败: sendMisId={}, rdMisId={}, sessionId={}, templateId={}, agentCode={}, error={}", sendMisId, rdMisId, sessionId, templateId, agentCode, errorMsg);
                return "发送失败: " + errorMsg;
            }
        } catch (Exception e) {
            log.error("大象消息发送异常: sendMisId={}, sessionId={}", sendMisId, sessionId, e);
            throw new RuntimeException("大象消息发送异常: " + e.getMessage(), e);
        }
    }

    /**
     * 构建Agent任务跳转URL
     *
     * @param sessionId 会话ID
     * @param agentCode Agent代码
     * @param taskDesc 任务描述
     * @return 拼接好的跳转URL
     */
    private String buildAgentTaskUrl(String sessionId, String agentCode, String taskDesc) {
        try {
            // 构造portal_locked_util_query_param参数的JSON内容
            String paramJson;
            if ("match_agent".equals(agentCode)) {
                taskDesc = "";
            }
            if (StringUtils.isNotBlank(taskDesc)) {
                // taskDesc不为空时，包含prdUrl字段
                paramJson = String.format(
                        "{\"sessionId\":\"%s\",\"agentCode\":\"%s\",\"prdUrl\":\"%s\"}",
                        sessionId, agentCode, taskDesc
                );
            } else {
                // taskDesc为空时，不包含prdUrl字段
                paramJson = String.format(
                        "{\"sessionId\":\"%s\",\"agentCode\":\"%s\"}",
                        sessionId, agentCode
                );
            }

            // URL编码
            String encodedParam = URLEncoder.encode(paramJson, "UTF-8");

            // 根据环境选择基础URL
            String baseUrl = getAgentPageBaseUrl();

            // 拼接完整URL
            String fullUrl = baseUrl + "?portal_locked_util_query_param=" + encodedParam;

            log.info("构建Agent任务URL: sessionId={}, agentCode={}, taskDesc={}, env={}, url={}",
                    sessionId, agentCode, taskDesc, getCurrentEnvironment(), fullUrl);

            return fullUrl;
        } catch (UnsupportedEncodingException e) {
            log.error("URL编码失败: sessionId={}, agentCode={}, taskDesc={}", sessionId, agentCode, taskDesc, e);
            throw new RuntimeException("URL编码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据agentCode发送预告消息
     *
     * @param misId 用户ID
     * @param agentCode Agent代码
     */
    private void sendPreNotificationMessage(String misId, String agentCode) {
        try {
            String preMessage = getPreNotificationMessage(agentCode);
            if (StringUtils.isBlank(preMessage)) {
                // 如果没有预告消息，直接返回
                return;
            }

            // 构造预告消息的变量值
            Map<String, String> preVariableValue = new HashMap<>();
            preVariableValue.put("txt", preMessage);

            // 构造预告消息的摘要文本（截断正文）
            String preAbstractText = preMessage.length() > 20 ? preMessage.substring(0, 20) + "..." : preMessage;

            // 发送预告消息（使用仅文本模板）
            SendMisCardRequest preRequest = new SendMisCardRequest();
            preRequest.setTemplateId(getTextTemplateId()); // 根据环境选择仅文本模板
            preRequest.setMisList(Arrays.asList(misId));
            preRequest.setAbstractText(preAbstractText);
            preRequest.setSessionId(null); // 预告消息不关联sessionId
            preRequest.setVariableValue(preVariableValue);

            TBaseReponse preResponse = openCardService.sendMisCard(preRequest);

            if (preResponse != null && preResponse.getCode() == 200) {
                log.info("预告消息发送成功: misId={}, agentCode={}", misId, agentCode);
            } else {
                log.warn("预告消息发送失败: misId={}, agentCode={}, error={}",
                        misId, agentCode, preResponse != null ? preResponse.getMessage() : "返回为空");
            }
        } catch (Exception e) {
            log.error("预告消息发送异常: misId={}, agentCode={}", misId, agentCode, e);
            // 预告消息发送失败不影响主消息发送，只记录日志
        }
    }

    /**
     * 根据agentCode获取预告消息内容
     *
     * @param agentCode Agent代码
     * @return 预告消息内容，如果没有则返回null
     */
    private String getPreNotificationMessage(String agentCode) {
        if ("parser_agent".equals(agentCode)) {
            return "您好！识别到您需要进行解析加工规则的需求分析。正在调用解析Agent完成此任务，预计5分钟完成，请耐心等待。";
        }
        // 其他agentCode暂时没有预告消息
        return null;
    }

    /**
     * 根据环境获取Agent页面基础URL
     *
     * @return Agent页面基础URL
     */
    private String getAgentPageBaseUrl() {
        HostEnv currentEnv = getCurrentEnvironment();

        // test或dev环境使用配置的URL，其他环境使用生产域名
        if (currentEnv == HostEnv.TEST || currentEnv == HostEnv.DEV) {
            return agentPageBaseUrl;
        } else {
            return "https://bml.sankuai.com/agent";
        }
    }

    /**
     * 获取当前环境
     *
     * @return 当前环境枚举
     */
    private HostEnv getCurrentEnvironment() {
        try {
            return MdpContextUtils.getHostEnv();
        } catch (Exception e) {
            log.warn("获取环境信息失败，默认使用PROD环境: {}", e.getMessage());
            return HostEnv.PROD;
        }
    }

    /**
     * 根据agentCode查询agent2rd获取对应的rdMis
     *
     * @param agentCode Agent代码
     * @return 对应的rdMis，如果没有找到则返回null
     */
    private String getRdMisByAgentCode(String agentCode) {
        try {
            // 优先从配置的映射中查找
            if (agent2rd != null && agent2rd.containsKey(agentCode)) {
                String rdMis = agent2rd.get(agentCode);
                log.info("从配置映射中找到rdMis: agentCode={}, rdMis={}", agentCode, rdMis);
                return rdMis;
            }

            log.info("未找到agentCode对应的rdMis映射: agentCode={}", agentCode);
            return null;
        } catch (Exception e) {
            log.error("查询agentCode对应的rdMis失败: agentCode={}", agentCode, e);
            return null;
        }
    }

    /**
     * 根据环境获取仅文本模板ID
     *
     * @return 仅文本模板ID
     */
    private Long getTextTemplateId() {
        HostEnv currentEnv = getCurrentEnvironment();
        if (currentEnv == HostEnv.TEST || currentEnv == HostEnv.DEV) {
            return offlineTextTemplateId; // 线下：22631
        } else {
            return onlineTextTemplateId; // 线上：21145
        }
    }

    /**
     * 根据环境获取文本+确认模板ID
     *
     * @return 文本+确认模板ID
     */
    private Long getConfirmTemplateId() {
        HostEnv currentEnv = getCurrentEnvironment();
        if (currentEnv == HostEnv.TEST || currentEnv == HostEnv.DEV) {
            return offlineConfirmTemplateId; // 线下：22630
        } else {
            return onlineConfirmTemplateId; // 线上：21144
        }
    }

    /**
     * 根据环境获取文本+输入+确认模板ID
     *
     * @return 文本+输入+确认模板ID
     */
    private Long getInputTemplateId() {
        HostEnv currentEnv = getCurrentEnvironment();
        if (currentEnv == HostEnv.TEST || currentEnv == HostEnv.DEV) {
            return offlineInputTemplateId; // 线下：22628
        } else {
            return onlineInputTemplateId; // 线上：21143
        }
    }
}
