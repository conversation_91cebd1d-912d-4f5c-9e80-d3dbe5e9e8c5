package com.sankuai.algoplatform.agentapi.domain.repository;

import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentNodeFlow;

import java.util.List;

/**
 * AgentNodeFlow数据访问对象接口
 *
 * <AUTHOR>
 * @date 2025年06月03日 19:19
 */
public interface AgentNodeFlowRepository {

    /**
     * 根据ID查询节点流转记录
     *
     * @param id 记录ID
     * @return 节点流转记录对象，如果不存在则返回null
     */
    AgentNodeFlow getById(Long id);

    /**
     * 根据会话ID查询节点流转记录列表（默认只查询状态正常的记录）
     *
     * @param sessionId 会话ID
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listBySessionId(String sessionId);

    /**
     * 根据会话ID查询节点流转记录列表
     *
     * @param sessionId 会话ID
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listBySessionId(String sessionId, boolean includeDeleted);

    /**
     * 根据消息ID查询节点流转记录列表（默认只查询状态正常的记录）
     *
     * @param msgId 消息ID
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listByMsgId(String msgId);

    /**
     * 根据消息ID查询节点流转记录列表
     *
     * @param msgId 消息ID
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listByMsgId(String msgId, boolean includeDeleted);

    /**
     * 根据会话ID和消息ID查询节点流转记录列表（默认只查询状态正常的记录）
     *
     * @param sessionId 会话ID
     * @param msgId 消息ID
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listBySessionIdAndMsgId(String sessionId, String msgId);

    /**
     * 根据会话ID和消息ID查询节点流转记录列表
     *
     * @param sessionId 会话ID
     * @param msgId 消息ID
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listBySessionIdAndMsgId(String sessionId, String msgId, boolean includeDeleted);

    /**
     * 根据当前节点名称查询节点流转记录列表（默认只查询状态正常的记录）
     *
     * @param currentNodeName 当前节点名称
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listByCurrentNodeName(String currentNodeName);

    /**
     * 根据当前节点名称查询节点流转记录列表
     *
     * @param currentNodeName 当前节点名称
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listByCurrentNodeName(String currentNodeName, boolean includeDeleted);

    /**
     * 根据会话ID和当前节点名称查询节点流转记录列表（默认只查询状态正常的记录）
     *
     * @param sessionId 会话ID
     * @param currentNodeName 当前节点名称
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listBySessionIdAndCurrentNodeName(String sessionId, String currentNodeName);

    /**
     * 根据会话ID和当前节点名称查询节点流转记录列表
     *
     * @param sessionId 会话ID
     * @param currentNodeName 当前节点名称
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listBySessionIdAndCurrentNodeName(String sessionId, String currentNodeName, boolean includeDeleted);

    /**
     * 根据下一个节点名称查询节点流转记录列表（默认只查询状态正常的记录）
     *
     * @param nextNodeName 下一个节点名称
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listByNextNodeName(String nextNodeName);

    /**
     * 根据下一个节点名称查询节点流转记录列表
     *
     * @param nextNodeName 下一个节点名称
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listByNextNodeName(String nextNodeName, boolean includeDeleted);

    /**
     * 根据当前节点状态查询节点流转记录列表（默认只查询状态正常的记录）
     *
     * @param currentNodeStatus 当前节点状态
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listByCurrentNodeStatus(String currentNodeStatus);

    /**
     * 根据当前节点状态查询节点流转记录列表
     *
     * @param currentNodeStatus 当前节点状态
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listByCurrentNodeStatus(String currentNodeStatus, boolean includeDeleted);

    /**
     * 插入新节点流转记录
     *
     * @param agentNodeFlow 节点流转记录对象
     * @return 影响的行数
     */
    int insert(AgentNodeFlow agentNodeFlow);

    /**
     * 批量插入节点流转记录
     *
     * @param agentNodeFlows 节点流转记录对象列表
     * @return 影响的行数
     */
    int batchInsert(List<AgentNodeFlow> agentNodeFlows);

    /**
     * 根据session_id, msg_id, current_node_name进行更新式插入
     *
     * @param agentNodeFlow 节点流转记录对象
     * @return 影响的行数
     */
    int insertOrUpdate(AgentNodeFlow agentNodeFlow);

    /**
     * 批量更新式插入
     *
     * @param agentNodeFlows 节点流转记录对象列表
     * @return 影响的行数
     */
    int batchInsertOrUpdate(List<AgentNodeFlow> agentNodeFlows);

    /**
     * 更新节点流转记录
     *
     * @param agentNodeFlow 节点流转记录对象
     * @return 影响的行数
     */
    int update(AgentNodeFlow agentNodeFlow);

    /**
     * 根据ID删除节点流转记录（物理删除）
     *
     * @param id 记录ID
     * @return 影响的行数
     */
    int deleteById(Long id);

    /**
     * 根据ID逻辑删除节点流转记录
     *
     * @param id 记录ID
     * @return 影响的行数
     */
    int logicDeleteById(Long id);

    /**
     * 根据会话ID删除所有节点流转记录（物理删除）
     *
     * @param sessionId 会话ID
     * @return 影响的行数
     */
    int deleteBySessionId(String sessionId);

    /**
     * 根据会话ID逻辑删除所有节点流转记录
     *
     * @param sessionId 会话ID
     * @return 影响的行数
     */
    int logicDeleteBySessionId(String sessionId);

    /**
     * 根据消息ID删除所有节点流转记录（物理删除）
     *
     * @param msgId 消息ID
     * @return 影响的行数
     */
    int deleteByMsgId(String msgId);

    /**
     * 根据消息ID逻辑删除所有节点流转记录
     *
     * @param msgId 消息ID
     * @return 影响的行数
     */
    int logicDeleteByMsgId(String msgId);

    /**
     * 分页查询会话的节点流转记录列表（默认只查询状态正常的记录）
     *
     * @param sessionId 会话ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> pageQueryBySessionId(String sessionId, int offset, int limit);

    /**
     * 分页查询会话的节点流转记录列表
     *
     * @param sessionId 会话ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> pageQueryBySessionId(String sessionId, int offset, int limit, boolean includeDeleted);

    /**
     * 计算会话的节点流转记录总数（默认只计算状态正常的记录）
     *
     * @param sessionId 会话ID
     * @return 节点流转记录总数
     */
    long countBySessionId(String sessionId);

    /**
     * 计算会话的节点流转记录总数
     *
     * @param sessionId 会话ID
     * @param includeDeleted 是否包含已删除的记录
     * @return 节点流转记录总数
     */
    long countBySessionId(String sessionId, boolean includeDeleted);

    /**
     * 根据当前节点名称查询节点流转记录列表
     *
     * @param sessionId       会话ID
     * @param currentNodeName 当前节点名称
     * @return 节点流转记录列表
     */
    List<AgentNodeFlow> listBySessionIdAndCurrentNodeNameDESC(String sessionId, String currentNodeName);
}
