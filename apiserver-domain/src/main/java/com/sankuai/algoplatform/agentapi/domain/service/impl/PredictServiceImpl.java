package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictService;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

@Service
public class PredictServiceImpl {
    @MdpThriftClient(remoteAppKey = "com.sankuai.algoplatform.predictor", remoteServerPort = 9001, timeout = 60000)
    private TPredictService.Iface predictService;

    public TPredictResponse predict(TPredictRequest request) throws TException {
        return predictService.predict(request);
    }
}
