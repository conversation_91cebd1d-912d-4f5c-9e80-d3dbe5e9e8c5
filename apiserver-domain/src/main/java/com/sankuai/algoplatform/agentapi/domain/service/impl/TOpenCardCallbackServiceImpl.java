package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.response.chat.TChatResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentChatService;
import com.sankuai.algoplatform.agentapi.infrastructure.constant.CommonConstants;
import com.sankuai.xm.openplatform.api.entity.*;
import com.sankuai.xm.openplatform.api.service.open.OpenCardServiceI;
import com.sankuai.xm.openplatform.api.service.sdk.OpenCardSeviceSI;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * Agent聊天服务实现
 *
 * <AUTHOR>
 * @date 2025年05月28日
 */
@Service
@MdpThriftServer(port = 9002)
@Slf4j
public class TOpenCardCallbackServiceImpl implements OpenCardSeviceSI.Iface {

    @Autowired
    TAgentChatService agentChatService;

    private static final String CARD_CALLBACK_SOURCE = "cardCallBack";

    @Value("${appId:6ee161f17b}")
    private String appId;
    @Value("${appSecret:5a09dc53c627446689e0b7ccb6cc3773}")
    private String appSecret;
    @Autowired
    private XmAuthServiceI.Iface xmAuthService;
    @Autowired
    private OpenCardServiceI.Iface openCardService;

    @Override
    public CardCallBackResp cardCallBack(CardCallBackReq cardCallBackReq) {
        try {
            log.info("接收到一体化卡片回调消息，cardCallBack，req={}", JsonUtils.toJson(cardCallBackReq));
            String interactiveParam = cardCallBackReq.getInteractiveParam();
            /*{
                "confirmCallback": "匹配RD点击了确认",
                "input": "RD已经完成了任务开发，请关注并跟进",
                "sessionId": "6e60e2d94ea145eb950b51075a75bdf1"
            }*/
            JSONObject jsonObject = JSON.parseObject(interactiveParam);
            String sessionId = jsonObject.getString("sessionId");
            if (StringUtils.isNotEmpty(sessionId)) {
                // 有sessionId发送消息
                AgentMessageDTO agentMessageDTO = new AgentMessageDTO();
                agentMessageDTO.setSource(CARD_CALLBACK_SOURCE);
                agentMessageDTO.setSessionId(sessionId);
                jsonObject.remove("sessionId");
                agentMessageDTO.setContent(JSON.toJSONString(jsonObject));
                log.info("cardCallBack，有sessionId调用chat接口，agentMessageDTO={}", JsonUtils.toJson(agentMessageDTO));
                TChatResponse tChatResponse = agentChatService.chat(CommonConstants.LONGTERMTASK_MISNAME, agentMessageDTO);
                log.info("cardCallBack，有sessionId调用chat接口，tChatResponse={}", JsonUtils.toJson(tChatResponse));
            }
            String requestId = cardCallBackReq.getRequestParam().getRequestId();
            Long templateId = jsonObject.getLong("templateId");
            if (Objects.nonNull(templateId)) {
                jsonObject.put("buttonEnable", "false");
                updateSharedCard(requestId, templateId, jsonObject);
            } else {
                log.info("templateId为空，不更新卡片，req={}", JsonUtils.toJson(cardCallBackReq));
            }
            CardCallBackResp res = new CardCallBackResp();
            RespStatus respStatus = new RespStatus();
            respStatus.setCode(ResCodeEnum.SUCCESS.getCode());
            respStatus.setMsg(ResCodeEnum.SUCCESS.getMsg());
            res.setStatus(respStatus);
            return res;
        } catch (Exception e) {
            log.error("cardCallBack error", e);
            CardCallBackResp res = new CardCallBackResp();
            RespStatus respStatus = new RespStatus();
            respStatus.setCode(ResCodeEnum.SERVICE_ERROR.getCode());
            respStatus.setMsg(ResCodeEnum.SERVICE_ERROR.getMsg());
            res.setStatus(respStatus);
            return res;
        }
    }

    @Override
    public PullCardResp pullCard(PullCardReq pullCardReq) throws TException {
        log.info("接收到一体化卡片回调消息，pullCard，req={}", JsonUtils.toJson(pullCardReq));
        return null;
    }

    public void updateSharedCard(String requestId, Long templateId, Map<String, Object> variableValue) {
        log.info("更新按钮状态,requestId:{},templateId:{},buttonEnable:{}", requestId, templateId, variableValue);
        String accessToken = getAccessToken(appId, appSecret);
        UpdateSharingCardReq updateSharingCardReq = new UpdateSharingCardReq();
        updateSharingCardReq.setRequestId(requestId);
        updateSharingCardReq.setTemplateId(templateId);
        updateSharingCardReq.setVariableValue(JsonUtils.toJson(variableValue));
        updateSharingCardReq.setVersion(System.currentTimeMillis());
        try {
            UpdateSharingCardResp updateSharingCardResp = openCardService.updateSharingCard(accessToken, updateSharingCardReq);
            log.info("更新卡片消息,req={},resp={}", JSON.toJSONString(updateSharingCardReq), JSON.toJSONString(updateSharingCardResp));
        } catch (TException e) {
            log.error("更新卡片消息异常,req={}", JSON.toJSONString(updateSharingCardReq), e);
            throw new RuntimeException(e);
        }
    }

    private String getAccessToken(String appId, String appSecret) {
        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppkey(appId);
        appAuthInfo.setAppSecret(appSecret);
        try {
            AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
            log.info("获取accessToken,resp={}", JSON.toJSONString(resp));
            if (resp.status.getCode() == 0) {
                return resp.getAccessToken().getToken();
            } else {
                throw new Exception("获取accessToken失败");
            }
        } catch (Exception e) {
            log.error("获取accessToken异常,appAuthInfo={}", JSON.toJSONString(appAuthInfo), e);
            return null;
        }
    }
}
