package com.sankuai.algoplatform.agentapi.domain.s3;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3KmsClient;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.exceptions.GetS3CredentialFailedAfterRetryException;
import com.dianping.cat.Cat;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.inf.octo.mns.model.HostEnv;

/**
 * <AUTHOR>
 * @date 2024年03月27日 4:59 下午
 */
public class AmazonS3ClientUtil {
    private static AmazonS3 bmlS3Client;

    public static AmazonS3 getBmlS3Client() throws GetS3CredentialFailedAfterRetryException {
        if (bmlS3Client == null) {
            String appkey = "com.sankuai.algoplatform.agentapi";
            HostEnv hostEnv = MdpContextUtils.getHostEnv();
            String hostname = "mss-shon.vip.sankuai.com";

            if (hostEnv == HostEnv.TEST) {
                hostname = "msstest.vip.sankuai.com";
            }
            return CreateAmazonS3Conn(appkey, hostname);
        } else {
            return bmlS3Client;
        }
    }

    //appkey: 服务秘钥对应的appkey
    //hostname:MSS的endpoint服务地址
    // AmazonS3是线程安全的，单例即可，千万不要每个请求创建一个client
    public static AmazonS3 CreateAmazonS3Conn (String appkey, String hostname) throws GetS3CredentialFailedAfterRetryException {
        ClientConfiguration configuration = new ClientConfiguration();
        // 默认协议为HTTPS。将这个值设置为Protocol.HTTP，则使用的是HTTP协议
        configuration.setProtocol(Protocol.HTTPS);

        //设置客户端生成的http请求hos格式，目前只支持path type的格式，不支持bucket域名的格式
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        // 目前s3只支持path style,下面选项需要设置为true
        s3ClientOptions.setPathStyleAccess(true);

        //生成云存储api client
        AmazonS3 s3client = new AmazonS3KmsClient(hostname, appkey ,configuration, s3ClientOptions);
        // AmazonS3 s3client = new AmazonS3KmsClient(hostname, appkey ,configuration);
        // AmazonS3 s3client = new AmazonS3KmsClient(hostname, appkey);

        return s3client;
    }
}
