package com.sankuai.algoplatform.agentapi.domain.repository.impl;

import com.sankuai.algoplatform.agentapi.domain.repository.AgentSessionRepository;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentSessionExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentSessionMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession;
import com.sankuai.algoplatform.agentapi.domain.enums.SessionStatusEnum;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * AgentSessionDAO接口实现类
 */
@Repository
public class AgentSessionRepositoryImpl implements AgentSessionRepository {

    @Resource
    private AgentSessionMapper agentSessionMapper;

    @Override
    public AgentSession getBySessionId(String sessionId) {
        AgentSession agentSession = agentSessionMapper.selectByPrimaryKey(sessionId);
        if (new Integer(-1).equals(agentSession.getStatus())) {
            return null;
        }
        return agentSession;
    }

    @Override
    public List<AgentSession> listByOwner(String owner) {
        AgentSessionExample example = new AgentSessionExample();
        example.createCriteria().andOwnerEqualTo(owner).andStatusNotEqualTo(SessionStatusEnum.DELETED.getCode());
        return agentSessionMapper.selectByExample(example);
    }

    @Override
    public List<AgentSession> listByAgentCode(String agentCode) {
        AgentSessionExample example = new AgentSessionExample();
        example.createCriteria().andAgentCodeEqualTo(agentCode).andStatusNotEqualTo(SessionStatusEnum.DELETED.getCode());
        return agentSessionMapper.selectByExample(example);
    }

    @Override
    public List<AgentSession> listByStatus(Integer status) {
        AgentSessionExample example = new AgentSessionExample();
        example.createCriteria().andStatusEqualTo(status);
        return agentSessionMapper.selectByExample(example);
    }

    @Override
    public int insert(AgentSession agentSession) {
        return agentSessionMapper.insert(agentSession);
    }

    @Override
    public int update(AgentSession agentSession) {
        return agentSessionMapper.updateByPrimaryKeySelective(agentSession);
    }

    @Override
    public int deleteBySessionId(String sessionId) {
        AgentSessionExample agentSessionExample = new AgentSessionExample();
        agentSessionExample.createCriteria().andSessionIdEqualTo(sessionId);
        AgentSession agentSession = new AgentSession();
        agentSession.setStatus(-1);
        return agentSessionMapper.updateByExampleSelective(agentSession, agentSessionExample);
    }

    @Override
    public List<AgentSession> pageQuery(String owner, String agentCode, Integer status, int offset, int limit) {
        AgentSessionExample example = createQueryExample(owner, agentCode, status);
        example.setOffset(offset);
        example.setRows(limit);
        example.setOrderByClause("updated_at DESC");
        return agentSessionMapper.selectByExample(example);
    }

    @Override
    public long countQuery(String owner, String agentCode, Integer status) {
        AgentSessionExample example = createQueryExample(owner, agentCode, status);
        return agentSessionMapper.countByExample(example);
    }

    /**
     * 创建查询条件
     *
     * @param owner     用户名
     * @param agentCode Agent代码
     * @param status    状态码
     * @return 查询条件
     */
    private AgentSessionExample createQueryExample(String owner, String agentCode, Integer status) {
        AgentSessionExample example = new AgentSessionExample();
        AgentSessionExample.Criteria criteria = example.createCriteria();

        if (StringUtils.hasText(owner)) {
            criteria.andOwnerEqualTo(owner);
        }

        if (StringUtils.hasText(agentCode)) {
            criteria.andAgentCodeEqualTo(agentCode);
        }

        if (status != null) {
            criteria.andStatusGreaterThan(status);
        } else {
            criteria.andStatusGreaterThan(-1);
        }

        return example;
    }
}