package com.sankuai.algoplatform.agentapi.domain.repository;

import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentMessage;

import java.util.List;

/**
 * AgentMessage 数据访问对象接口
 */
public interface AgentMessageRepository {
    /**
     * 根据消息ID查询消息
     *
     * @param msgId 消息ID
     * @return 消息对象，如果不存在则返回null
     */
    AgentMessage getByMsgId(String msgId);

    /**
     * 根据会话ID查询消息列表
     *
     * @param sessionId 会话ID
     * @return 消息列表
     */
    List<AgentMessage> listBySessionId(String sessionId);
    /**
     * 根据会话ID和消息类型查询消息列表
     *
     * @param sessionId 会话ID
     * @param messageType 消息类型
     * @return 消息列表
     */
    List<AgentMessage> listBySessionIdAndMessageType(String sessionId, String messageType);
    /**
     * 根据会话ID和消息来源查询消息列表
     *
     * @param sessionId 会话ID
     * @param source 消息来源
     * @return 消息列表
     */
    List<AgentMessage> listBySessionIdAndSource(String sessionId, String source);
    /**
     * 插入新消息
     *
     * @param agentMessage 消息对象
     * @return 影响的行数
     */
    int insert(AgentMessage agentMessage);

    /**
     * 插入新消息
     *
     * @param agentMessages 消息对象
     * @return 影响的行数
     */
    int batchInsert(List<AgentMessage> agentMessages);

    /**
     * 根据msg_id进行更新式插入
     *
     * @param agentMessage 消息对象
     * @return 影响的行数
     */
    int insertOrUpdate(AgentMessage agentMessage);

    /**
     * 批量更新式插入
     *
     * @param agentMessages 消息对象列表
     * @return 影响的行数
     */
    int batchInsertOrUpdate(List<AgentMessage> agentMessages);

    /**
     * 更新消息
     *
     * @param agentMessage 消息对象
     * @return 影响的行数
     */
    int update(AgentMessage agentMessage);
    /**
     * 根据消息ID删除消息
     *
     * @param msgId 消息ID
     * @return 影响的行数
     */
    int deleteByMsgId(String msgId);

    /**
     * 根据会话ID删除所有消息
     *
     * @param sessionId 会话ID
     * @return 影响的行数
     */
    int deleteBySessionId(String sessionId);
    /**
     * 分页查询会话的消息列表
     *
     * @param sessionId 会话ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 消息列表
     */
    List<AgentMessage> pageQueryBySessionId(String sessionId, int offset, int limit);
    /**
     * 计算会话的消息总数
     *
     * @param sessionId 会话ID
     * @return 消息总数
     */
    long countBySessionId(String sessionId);

    /**
     * 判断是否是最后一页
     *
     * @param sessionId 会话ID
     * @param startId 起始消息ID
     * @param pageSize 每页数量
     * @return 是否最后一页
     */

    boolean isLastPageByStartId(String sessionId, Long startId, int pageSize);

    /**
     *
     * @param sessionId 会话ID
     * @param pageSize 每页数量
     * @return 是否最后一页
     */
    boolean isLastPageByStartMsgId(String sessionId,  int pageSize);

    /**
     * 查询消息列表
     *
     * @param sessionId 会话ID
     * @param startId 起始消息ID
     * @param pageSize 每页数量
     *
     * @return 消息列表
     */
    List<AgentMessage> pageQueryByStartId(String sessionId, Long startId, int pageSize);

    /**
     * 查询消息列表
     *
     * @param sessionId 会话ID
     * @param pageSize 每页数量
     * @return 消息列表
     */
    List<AgentMessage> pageQueryByStartMsgId(String sessionId,  int pageSize);

    /**
     *
     * @param sessionId 会话ID
     * @param startMsgId 起始消息ID
     * @return 起始数据库记录ID
     */
    Long getStartId(String sessionId,String startMsgId);
}