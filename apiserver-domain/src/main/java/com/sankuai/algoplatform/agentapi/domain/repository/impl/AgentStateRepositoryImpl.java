package com.sankuai.algoplatform.agentapi.domain.repository.impl;

import com.sankuai.algoplatform.agentapi.domain.repository.AgentStateRepository;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.example.AgentStateExample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.mapper.AgentStateMapper;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * AgentStateDAO接口实现类
 */
@Repository
public class AgentStateRepositoryImpl implements AgentStateRepository {

    @Resource
    private AgentStateMapper agentStateMapper;

    @Override
    public AgentState getById(Long id) {
        return agentStateMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AgentState> listBySessionId(String sessionId) {
        AgentStateExample example = new AgentStateExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        example.setOrderByClause("add_time DESC");
        return agentStateMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public AgentState getBySessionIdAndVersionId(String sessionId, String versionId) {
        AgentStateExample example = new AgentStateExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andVersionIdEqualTo(versionId);
        List<AgentState> states = agentStateMapper.selectByExampleWithBLOBs(example);
        return states.isEmpty() ? null : states.get(0);
    }

    @Override
    public int insert(AgentState agentState) {
        return agentStateMapper.insert(agentState);
    }

    @Override
    public int update(AgentState agentState) {
        return agentStateMapper.updateByPrimaryKeySelective(agentState);
    }

    @Override
    public int deleteById(Long id) {
        return agentStateMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteBySessionId(String sessionId) {
        AgentStateExample example = new AgentStateExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        return agentStateMapper.deleteByExample(example);
    }

    @Override
    public AgentState getLatestBySessionId(String sessionId) {
        AgentStateExample example = new AgentStateExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        example.setOrderByClause("add_time DESC");
        example.setRows(1);
        List<AgentState> states = agentStateMapper.selectByExampleWithBLOBs(example);
        return states.isEmpty() ? null : states.get(0);
    }
}