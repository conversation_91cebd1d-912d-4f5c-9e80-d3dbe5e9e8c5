package com.sankuai.algoplatform.agentapi.domain.service.impl;

import com.dianping.cat.Cat;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.algoplatform.agentapi.client.dto.AgentSessionDTO;
import com.sankuai.algoplatform.agentapi.client.dto.PageInfo;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.TStringResponse;
import com.sankuai.algoplatform.agentapi.client.response.session.TSessionBatchResponse;
import com.sankuai.algoplatform.agentapi.client.response.session.TSessionResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentSessionService;
import com.sankuai.algoplatform.agentapi.domain.enums.SessionStatusEnum;
import com.sankuai.algoplatform.agentapi.domain.repository.AgentSessionRepository;
import com.sankuai.algoplatform.agentapi.domain.tair.TairClient;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.AgentSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * AgentSessionService 接口实现类
 */
@Service
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Slf4j
public class TAgentSessionServiceImpl implements TAgentSessionService {

    @Autowired
    private AgentSessionRepository agentSessionRepository;

    @Autowired
    private TairClient tairClient;

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public TSessionBatchResponse getAgentSessions(String misId, String agentCode, Integer pageNo, Integer pageSize) {
        try {
            long total = agentSessionRepository.countQuery(null, agentCode, null);

            if (StringUtils.isEmpty(misId)) {
                return TSessionBatchResponse.fail(500, "misId为空");
            }

            if (StringUtils.isEmpty(agentCode)) {
                return TSessionBatchResponse.fail(500, "agentCode为空");
            }

            pageSize = pageSize == null? 10: pageSize;
            pageSize = pageSize > 50 ? 50: pageSize;
            pageNo = (pageSize == null || pageNo < 1) ? 1: pageNo;
            List<AgentSession> sessions = agentSessionRepository.pageQuery(misId, agentCode, null, (pageNo - 1)*pageSize, pageSize);
            List<AgentSessionDTO> agentSessionDTOS = convertToDTOList(sessions);
            TSessionBatchResponse tSessionBatchResponse = new TSessionBatchResponse();
            tSessionBatchResponse.setCode(200);
            tSessionBatchResponse.setData(agentSessionDTOS);
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageNum(pageNo);
            pageInfo.setPageSize(pageSize);
            pageInfo.setTotal(total);
            pageInfo.calculatePages();
            tSessionBatchResponse.setPageInfo(pageInfo);
            return tSessionBatchResponse;
        } catch (Exception e) {
            log.error("获取Agent会话列表失败: misId={}, agentCode={}", misId, agentCode, e);
            TSessionBatchResponse tSessionBatchResponse = new TSessionBatchResponse();
            tSessionBatchResponse.setCode(500);
            tSessionBatchResponse.setMessage("获取Agent会话列表失败: " + e.getMessage());
            return tSessionBatchResponse;
        }
    }

    @Override
    public TSessionResponse getBySessionId(String sessionId) {
        try {
            AgentSession session = agentSessionRepository.getBySessionId(sessionId);
            return TSessionResponse.success(session != null ? convertToDTO(session) : null);
        } catch (Exception e) {
            log.error("获取会话详情失败: sessionId={}", sessionId, e);
            return TSessionResponse.fail("获取会话详情失败: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse saveSession(AgentSessionDTO sessionDTO) {
        try {
            AgentSession session = convertToModel(sessionDTO);
            boolean result = agentSessionRepository.insert(session) == 1;
            return TCheckResponse.success(result);
        } catch (Exception e) {
            log.error("保存会话失败: sessionId={}", sessionDTO != null ? sessionDTO.getSessionId() : "null", e);
            return TCheckResponse.fail("保存会话失败: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse deleteSession(String sessionId) {
        try {
            boolean result = agentSessionRepository.deleteBySessionId(sessionId) == 1;
            return TCheckResponse.success(result);
        } catch (Exception e) {
            log.error("删除会话失败: sessionId={}", sessionId, e);
            return TCheckResponse.fail("删除会话失败: " + e.getMessage());
        }
    }

    @Override
    public TStringResponse createSession(String owner, String agentCode) {
        try {
            // 生成唯一的会话ID
            String sessionId = UUID.randomUUID().toString().replace("-", "");

            // 创建会话对象
            AgentSession session = AgentSession.builder()
                    .sessionId(sessionId)
                    .owner(owner)
                    .agentCode(agentCode)
                    .title("新建会话"+ sessionId)
                    .updatedAt(new Date())
                    .status(SessionStatusEnum.PRE_ALLOCATE.getCode())
                    .showMessage("")
                    .build();

            // 保存会话
            boolean result = agentSessionRepository.insert(session) == 1;
            if (result) {
                log.info("创建会话成功: sessionId={}, owner={}, agentCode={}", sessionId, owner, agentCode);
                return TStringResponse.success(sessionId);
            } else {
                log.error("创建会话失败: sessionId={}, owner={}, agentCode={}", sessionId, owner, agentCode);
                return TStringResponse.fail("创建会话失败");
            }
        } catch (Exception e) {
            log.error("创建会话异常: owner={}, agentCode={}", owner, agentCode, e);
            return TStringResponse.fail("创建会话异常: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse updateSessionStatus(String sessionId, Integer status) {
        try {
            // 先查询会话是否存在
            AgentSession session = agentSessionRepository.getBySessionId(sessionId);
            if (session == null) {
                return TCheckResponse.fail("会话不存在");
            }

            // 更新会话状态
            session.setStatus(status);
            session.setUpdatedAt(new Date()); // 同时更新更新时间

            // 执行更新操作
            boolean result = agentSessionRepository.update(session) == 1;
            return TCheckResponse.success(result);
        } catch (Exception e) {
            log.error("更新会话状态异常", e);
            return TCheckResponse.fail("更新会话状态异常: " + e.getMessage());
        }
    }

    @Override
    public TCheckResponse updateSessionTitle(String sessionId, String title) {
        try{
            // 先查询会话是否存在
            AgentSession session = agentSessionRepository.getBySessionId(sessionId);
            if (session == null) {
                return TCheckResponse.fail("会话不存在");
            }
            session.setTitle(title);
            session.setUpdatedAt(new Date()); // 同时更新更新时间

            // 执行更新操作
            boolean result = agentSessionRepository.update(session) == 1;
            return TCheckResponse.success(result);
        } catch (Exception e) {
            log.error("更新会话标题异常", e);
            return TCheckResponse.fail("更新会话标题异常: " + e.getMessage());
        }
    }



    /**
     * 将领域模型转换为DTO
     *
     * @param session 领域模型
     * @return DTO
     */
    private AgentSessionDTO convertToDTO(AgentSession session) {
        if (session == null) {
            return null;
        }
        AgentSessionDTO dto = new AgentSessionDTO();
        BeanUtils.copyProperties(session, dto);

        // 特殊处理Date类型转String
        if (session.getUpdatedAt() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
            dto.setUpdatedAt(sdf.format(session.getUpdatedAt()));
        }

        return dto;
    }

    /**
     * 将DTO转换为领域模型
     *
     * @param dto DTO
     * @return 领域模型
     */
    private AgentSession convertToModel(AgentSessionDTO dto) {
        if (dto == null) {
            return null;
        }
        AgentSession model = new AgentSession();
        BeanUtils.copyProperties(dto, model);
        return model;
    }

    /**
     * 将领域模型列表转换为DTO列表
     *
     * @param sessions 领域模型列表
     * @return DTO列表
     */
    private List<AgentSessionDTO> convertToDTOList(List<AgentSession> sessions) {
        if (sessions == null || sessions.isEmpty()) {
            return new ArrayList<>();
        }
        return sessions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Session连接维护相关方法
     */

    /**
     * 获取Session对应的目标IP
     * 优先使用Tair中的映射关系，如果没有则返回原始IP（不保存映射）
     *
     * @param sessionId 会话ID
     * @param originalIp 新获取的IP
     * @return 目标IP
     */
    public String getTargetIpForSession(String sessionId, String originalIp) {
        try {
            // 先从Tair获取已有的映射关系
            String mappedIp = tairClient.getSessionIpMapping(sessionId);
            if (mappedIp != null && !mappedIp.isEmpty()) {
                log.info("使用Tair中的IP映射: sessionId={}, mappedIp={}", sessionId, mappedIp);
                return mappedIp;
            } else {
                // 没有映射关系，返回原始IP但不保存映射关系
                log.info("没有找到IP映射，返回原始IP: sessionId={}, originalIp={}", sessionId, originalIp);
                return originalIp;
            }
        } catch (Exception e) {
            log.error("获取目标IP失败，使用原始IP: sessionId={}, originalIp={}", sessionId, originalIp, e);
            return originalIp;
        }
    }

    /**
     * 更新Session与IP的映射关系（仅在连接成功后调用）
     *
     * @param sessionId 会话ID
     * @param ip 成功连接的IP地址
     * @return 更新是否成功
     */
    public boolean updateSessionIpMapping(String sessionId, String ip) {
        try {
            boolean result = tairClient.setSessionIpMapping(sessionId, ip);
            log.info("更新Session IP映射: sessionId={}, ip={}, result={}", sessionId, ip, result);
            return result;
        } catch (Exception e) {
            log.error("更新Session IP映射失败: sessionId={}, ip={}", sessionId, ip, e);
            return false;
        }
    }

    /**
     * 删除Session对应的IP映射关系
     *
     * @param sessionId 会话ID
     * @return 删除是否成功
     */
    public boolean removeSessionIpMapping(String sessionId) {
        try {
            boolean result = tairClient.removeSessionIpMapping(sessionId);
            log.info("删除Session IP映射: sessionId={}, result={}", sessionId, result);
            return result;
        } catch (Exception e) {
            log.error("删除Session IP映射失败: sessionId={}", sessionId, e);
            return false;
        }
    }
}