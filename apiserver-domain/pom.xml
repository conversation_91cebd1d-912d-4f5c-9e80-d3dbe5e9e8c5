<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.algoplatform.agentapi</groupId>
        <artifactId>apiserver</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>apiserver-domain</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>apiserver-domain</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.algoplatform.agentapi</groupId>
            <artifactId>apiserver-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.algoplatform.agentapi</groupId>
            <artifactId>apiserver-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meishi.stgy.algoplatform</groupId>
            <artifactId>predictor-client</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>martgeneral-infer</groupId>
            <artifactId>martgeneral-infer-api</artifactId>
            <version>1.2.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>activation</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jaxb-api</artifactId>
                    <groupId>javax.xml.bind</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.activation-api</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
            <artifactId>open-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
        </dependency>
    </dependencies>

</project>
