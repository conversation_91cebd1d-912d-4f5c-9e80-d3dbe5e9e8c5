<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.algoplatform.agentapi</groupId>
        <artifactId>apiserver</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>apiserver-client</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>apiserver-api</name>

    <properties>
        <java.version>8</java.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>mdp-doc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.30</version>
            <scope>provided</scope> <!-- 如果只为编译，不参与打包，可用 provided -->
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>

        </dependency>

        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
            <version>1.0.8</version>
        </dependency>
    </dependencies>

</project>
