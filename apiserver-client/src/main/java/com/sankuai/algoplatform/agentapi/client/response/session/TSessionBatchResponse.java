package com.sankuai.algoplatform.agentapi.client.response.session;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentSessionDTO;
import com.sankuai.algoplatform.agentapi.client.dto.PageInfo;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月09日 11:38
 */
@ThriftStruct
@Data
public class TSessionBatchResponse extends TBaseReponse {
    @FieldDoc(description = "session列表", example = {})
    @ThriftField(3)
    List<AgentSessionDTO> data;

    @FieldDoc(description = "分页信息", example = {})
    @ThriftField(4)
    PageInfo pageInfo;

    public static TSessionBatchResponse fail(Integer code ,String message) {
        TSessionBatchResponse tSessionBatchResponse = new TSessionBatchResponse();
        tSessionBatchResponse.setCode(code);
        tSessionBatchResponse.setMessage(message);
        return tSessionBatchResponse;
    }
}
