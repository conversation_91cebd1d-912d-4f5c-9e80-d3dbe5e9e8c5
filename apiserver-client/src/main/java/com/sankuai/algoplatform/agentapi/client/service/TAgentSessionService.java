package com.sankuai.algoplatform.agentapi.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentSessionDTO;
import com.sankuai.algoplatform.agentapi.client.response.Response;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.TStringResponse;
import com.sankuai.algoplatform.agentapi.client.response.session.TSessionBatchResponse;
import com.sankuai.algoplatform.agentapi.client.response.session.TSessionResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月25日 14:34
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "Agent会话管理接口", scenarios = "")
public interface TAgentSessionService {
    /**
     * 根据Agent代码查询会话列表
     *
     * @param agentCode Agent代码
     * @return 会话列表
     */
    @ThriftMethod
    @MethodDoc(description = "根据Agent代码查询会话列表")
    TSessionBatchResponse getAgentSessions(String misId, String agentCode, Integer pageNo, Integer pageSize);

    /**
     * 根据会话ID查询会话
     *
     * @param sessionId 会话ID
     * @return 会话对象，如果不存在则返回null
     */
    @ThriftMethod
    @MethodDoc(description = "根据会话ID查询会话")
    TSessionResponse getBySessionId(String sessionId);

    /**
     * 保存会话
     *
     * @param sessionDTO 会话DTO
     * @return 是否成功
     */
    @ThriftMethod
    @MethodDoc(description = "保存会话")
    TCheckResponse saveSession(AgentSessionDTO sessionDTO);

    /**
     * 删除会话
     *
     * @param sessionId 会话ID
     * @return 是否成功
     */
    @ThriftMethod
    @MethodDoc(description = "删除会话")
    TCheckResponse deleteSession(String sessionId);

    /**
     * 创建新会话
     *
     * @param owner 用户名
     * @param agentCode Agent代码
     * @return 新创建的会话ID
     */
    @ThriftMethod
    @MethodDoc(description = "新建session")
    TStringResponse createSession(String owner, String agentCode);

    /**
     * 更新会话状态
     *
     * @param sessionId 会话ID
     * @param status 新的状态值
     * @return 是否更新成功
     */
    @ThriftMethod
    @MethodDoc(description = "更新session状态")
    TCheckResponse updateSessionStatus(String sessionId, Integer status);

    /**
     * 更新会话状态
     *
     * @param sessionId 会话ID
     * @param title 新的标题值
     * @return 是否更新成功
     */
    @ThriftMethod
    @MethodDoc(description = "更新session标题")
    TCheckResponse updateSessionTitle(String sessionId, String title);
}
