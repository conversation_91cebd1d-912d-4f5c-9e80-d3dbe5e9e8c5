package com.sankuai.algoplatform.agentapi.client.response.node;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentNodeFlowDTO;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月12日 13:37
 */
@ThriftStruct
@Data
public class TNodesResponse extends TBaseReponse {
    @FieldDoc(description = "节点列表", example = {})
    @ThriftField(3)
    List<AgentNodeFlowDTO> data;

    public static TNodesResponse success(List<AgentNodeFlowDTO> data) {
        TNodesResponse tNodesResponse = new TNodesResponse();
        tNodesResponse.setCode(200);
        tNodesResponse.setData(data);
        return tNodesResponse;
    }

    public static TNodesResponse fail(Integer code, String message) {
        TNodesResponse tNodesResponse = new TNodesResponse();
        tNodesResponse.setCode(code);
        tNodesResponse.setMessage(message);
        return tNodesResponse;
    }
}
