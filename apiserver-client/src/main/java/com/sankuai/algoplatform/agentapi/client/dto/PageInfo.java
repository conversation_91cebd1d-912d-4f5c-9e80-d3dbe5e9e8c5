package com.sankuai.algoplatform.agentapi.client.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.io.Serializable;

/**
 * 分页信息类
 * <AUTHOR>
 * @date 2025年05月25日 16:50
 */
@ThriftStruct
@Data
public class PageInfo{
    /**
     * 当前页码
     */
    @ThriftField(1)
    private Integer pageNum;

    /**
     * 每页数量
     */
    @ThriftField(2)
    private Integer pageSize;

    /**
     * 总记录数
     */
    @ThriftField(3)
    private Long total;

    /**
     * 总页数
     */
    @ThriftField(4)
    private Integer pages;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    @ThriftField(5)
    private Boolean hasPrev;

    /**
     * 计算总页数
     */
    public void calculatePages() {
        if (total != null && pageSize > 0) {
            this.pages = (int) Math.ceil((double) total / pageSize);
            this.hasNext = pageNum < pages;
            this.hasPrev = pageNum > 1;
        }
    }
}
