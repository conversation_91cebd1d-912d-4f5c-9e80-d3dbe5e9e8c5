package com.sankuai.algoplatform.agentapi.client.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 发送MIS卡片请求对象
 *
 * <AUTHOR>
 * @date 2025年01月14日
 */
@ThriftStruct
@Data
public class SendMisCardRequest {

    /**
     * 模版标识id
     */
    @FieldDoc(description = "模版标识id", example = {})
    @ThriftField(1)
    private Long templateId;

    /**
     * mis列表
     */
    @FieldDoc(description = "mis列表", example = {})
    @ThriftField(2)
    private List<String> misList;

    /**
     * 摘要
     */
    @FieldDoc(description = "摘要", example = {})
    @ThriftField(3)
    private String abstractText;

    /**
     * sessionId
     */
    @FieldDoc(description = "sessionId", example = {})
    @ThriftField(4)
    private String sessionId;

    /**
     * 变量（txt、confirmCallback）
     */
    @FieldDoc(description = "变量（txt、confirmCallback）", example = {})
    @ThriftField(5)
    private Map<String, String> variableValue;
}

