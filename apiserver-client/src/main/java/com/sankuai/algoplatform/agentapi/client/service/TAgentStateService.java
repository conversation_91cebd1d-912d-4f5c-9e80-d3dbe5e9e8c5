package com.sankuai.algoplatform.agentapi.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentStateDTO;
import com.sankuai.algoplatform.agentapi.client.response.Response;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.state.TStateResponse;

/**
 * Agent状态服务接口
 * <AUTHOR>
 * @date 2025年05月25日 14:35
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "Agent状态管理接口", scenarios = "")
public interface TAgentStateService {
    /**
     * 获取Agent状态
     *
     * @param sessionId 会话ID
     * @return Agent状态信息
     */
    @ThriftMethod
    @MethodDoc(description = "获取Agent状态")
    TStateResponse getAgentState(String sessionId);

    /**
     * 保存Agent状态
     *
     * @param agentStateDTO Agent状态DTO
     * @return 是否成功
     */
    @ThriftMethod
    @MethodDoc(description = "保存Agent状态")
    TCheckResponse saveAgentState(AgentStateDTO agentStateDTO);
}
