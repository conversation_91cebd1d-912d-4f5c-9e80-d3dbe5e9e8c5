package com.sankuai.algoplatform.agentapi.client.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年05月25日 14:56
 */
@ThriftStruct
@Data
public class AgentStateDTO{
    /**
     *   字段: session_id
     *   说明: 会话唯一标识
     */
    @ThriftField(1)
    private String sessionId;

    /**
     *   字段: version_id
     *   说明: 版本号
     */
    @ThriftField(2)
    private String versionId;

    /**
     *   字段: add_time
     *   说明: 保存时间
     */
    @ThriftField(3)
    private Date addTime;

    /**
     *   字段: team_state
     */
    @ThriftField(4)
    private String teamState;
}
