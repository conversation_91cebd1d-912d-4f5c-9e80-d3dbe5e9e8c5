package com.sankuai.algoplatform.agentapi.client.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * Agent节点流转DTO
 */
@ThriftStruct
@Data
public class AgentNodeFlowDTO{
    /**
     * 记录ID
     */
    @ThriftField(1)
    private Long id;

    /**
     * 会话唯一标识
     */
    @ThriftField(2)
    private String sessionId;

    /**
     * 消息唯一标识
     */
    @ThriftField(3)
    private String msgId;

    /**
     * 当前节点名称
     */
    @ThriftField(4)
    private String currentNodeName;

    /**
     * 下一个节点名称
     */
    @ThriftField(5)
    private String nextNodeName;

    /**
     * 当前节点状态
     */
    @ThriftField(6)
    private String currentNodeStatus;

    /**
     * 添加时间
     */
    @ThriftField(7)
    private String addTime;

    /**
     * 节点日志
     */
    @ThriftField(8)
    private String nodeLog;
}