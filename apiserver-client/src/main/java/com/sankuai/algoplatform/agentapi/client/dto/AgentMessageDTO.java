package com.sankuai.algoplatform.agentapi.client.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年05月25日 14:59
 */
@ThriftStruct
@Data
public class AgentMessageDTO{
    /**
     *   字段: msg_id
     *   说明: 消息唯一标识
     */
    @ThriftField(1)
    private String msgId;

    /**
     *   字段: session_id
     *   说明: 归属会话
     */
    @ThriftField(2)
    private String sessionId;

    /**
     *   字段: message_type
     *   说明: 消息类型
     */
    @ThriftField(3)
    private String messageType;

    /**
     *   字段: source
     *   说明: 消息来源, agent内角色名称
     */
    @ThriftField(4)
    private String source;

    /**
     *   字段: attachment
     *   说明: 附件链接，逗号拼接
     */
    @ThriftField(5)
    private String attachment;

    /**
     *   字段: attachment_type
     *   说明: 附件类型
     */
    @ThriftField(6)
    private String attachmentType;

    /**
     *   字段: content
     *   说明: 消息内容
     */
    @ThriftField(7)
    private String content;

    /**
     *  字段: add_time
     *  说明: 消息添加时间
     */
    @ThriftField(8)
    private String addTime;
}
