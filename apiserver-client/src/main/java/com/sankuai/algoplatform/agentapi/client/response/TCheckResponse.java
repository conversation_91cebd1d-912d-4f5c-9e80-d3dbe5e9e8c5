package com.sankuai.algoplatform.agentapi.client.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年06月09日 16:02
 */
@ThriftStruct
@Data
public class TCheckResponse extends TBaseReponse{
    @FieldDoc(description = "请求状态", example = {})
    @ThriftField(3)
    Boolean data;

    public static TCheckResponse success(Boolean status) {
        TCheckResponse tCheckResponse = new TCheckResponse();
        tCheckResponse.setCode(200);
        tCheckResponse.setData(status);
        return tCheckResponse;
    }

    public static TCheckResponse fail(String message) {
        TCheckResponse tCheckResponse = new TCheckResponse();
        tCheckResponse.setCode(500);
        tCheckResponse.setData(false);
        tCheckResponse.setMessage(message);
        return tCheckResponse;
    }
}
