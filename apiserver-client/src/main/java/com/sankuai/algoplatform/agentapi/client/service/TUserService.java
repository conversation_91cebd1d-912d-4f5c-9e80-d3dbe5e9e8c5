package com.sankuai.algoplatform.agentapi.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.response.chat.TChatResponse;
import com.sankuai.algoplatform.agentapi.client.response.user.TUserInfoResponse;

/**
 * <AUTHOR>
 * @date 2025年06月18日 16:35
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "用户信息获取接口", scenarios = "")
public interface TUserService {
    /**
     * 获取用户信息
     * @param misId 用户ID
     * @return 响应结果
     */
    @ThriftMethod
    @MethodDoc(description = "获取用户信息")
    TUserInfoResponse getUserInfo(String misId);
}
