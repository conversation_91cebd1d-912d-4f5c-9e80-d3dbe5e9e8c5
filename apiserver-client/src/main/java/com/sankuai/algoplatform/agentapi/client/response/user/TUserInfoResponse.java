package com.sankuai.algoplatform.agentapi.client.response.user;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentStateDTO;
import com.sankuai.algoplatform.agentapi.client.dto.UserInfo;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年06月18日 16:36
 */
@ThriftStruct
@Data
public class TUserInfoResponse extends TBaseReponse {
    @FieldDoc(description = "用户信息", example = {})
    @ThriftField(3)
    UserInfo data;
}
