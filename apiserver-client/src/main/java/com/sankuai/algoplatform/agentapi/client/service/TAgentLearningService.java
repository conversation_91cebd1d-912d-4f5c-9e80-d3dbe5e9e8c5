package com.sankuai.algoplatform.agentapi.client.service;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.request.TKnowledgeCollectRequest;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import org.apache.thrift.TException;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "Agent持续学习接口", scenarios = "")
public interface TAgentLearningService {

    @ThriftMethod
    @MethodDoc(description = "收集知识供评测")
    TBaseReponse collectKnowledges(TKnowledgeCollectRequest req) throws TException;
}
