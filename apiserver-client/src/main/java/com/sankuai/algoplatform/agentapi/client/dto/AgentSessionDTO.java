package com.sankuai.algoplatform.agentapi.client.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年05月25日 14:58
 */
@ThriftStruct
@Data
public class AgentSessionDTO {
    /**
     *   字段: session_id
     *   说明: 会话唯一标识
     */
    @ThriftField(1)
    private String sessionId;

    /**
     *   字段: owner
     *   说明: 会话创建者
     */
    @ThriftField(2)
    private String owner;

    /**
     *   字段: agent_code
     *   说明: Agent标识
     */
    @ThriftField(3)
    private String agentCode;

    /**
     *   字段: title
     *   说明: 标题
     */
    @ThriftField(4)
    private String title;

    /**
     *   字段: updated_at
     *   说明: 更新时间(最新消息时间)
     */
    @ThriftField(5)
    private String updatedAt;

    /**
     *   字段: status
     *   说明: 会话状态 0: 执行中 1: 执行成功 2: 执行失败 3: 暂停中
     */
    @ThriftField(6)
    private Integer status;

    /**
     *   字段: show_message
     *   说明: 缩略信息
     */
    @ThriftField(7)
    private String showMessage;
}
