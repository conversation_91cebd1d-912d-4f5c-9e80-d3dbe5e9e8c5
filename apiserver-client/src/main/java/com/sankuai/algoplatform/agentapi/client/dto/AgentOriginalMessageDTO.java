package com.sankuai.algoplatform.agentapi.client.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年05月25日 15:24
 */
@ThriftStruct
@Data
public class AgentOriginalMessageDTO{
    /**
     * 消息唯一标识
     */
    @ThriftField(1)
    private String msgId;

    /**
     * 会话唯一标识
     */
    @ThriftField(2)
    private String sessionId;

    /**
     * 消息来源，agent内角色
     */
    @ThriftField(3)
    private String source;

    /**
     * 模型使用信息
     */
    @ThriftField(4)
    private String modelUsage;

    /**
     * 消息类型，agent框架内类型
     */
    @ThriftField(5)
    private String type;

    /**
     *   字段: meta_data
     *   说明: 元数据
     */
    @ThriftField(6)
    private String metaData;

    /**
     *   字段: content
     *   说明: 消息内容
     */
    @ThriftField(7)
    private String content;
}
