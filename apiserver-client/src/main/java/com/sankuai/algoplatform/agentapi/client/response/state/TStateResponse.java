package com.sankuai.algoplatform.agentapi.client.response.state;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentStateDTO;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年06月10日 13:51
 */
@ThriftStruct
@Data
public class TStateResponse extends TBaseReponse {
    @FieldDoc(description = "agent状态", example = {})
    @ThriftField(3)
    AgentStateDTO data;

    public static TStateResponse success(AgentStateDTO data) {
        TStateResponse tStateResponse = new TStateResponse();
        tStateResponse.setCode(200);
        tStateResponse.setData(data);
        return tStateResponse;
    }

    public static TStateResponse fail(String message) {
        TStateResponse tStateResponse = new TStateResponse();
        tStateResponse.setCode(500);
        tStateResponse.setMessage(message);
        return tStateResponse;
    }
}
