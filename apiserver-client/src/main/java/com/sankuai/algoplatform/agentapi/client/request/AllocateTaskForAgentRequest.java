package com.sankuai.algoplatform.agentapi.client.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * 给Agent委派任务请求对象
 *
 * <AUTHOR>
 * @date 2025年07月08日
 */
@ThriftStruct
@Data
public class AllocateTaskForAgentRequest {

    /**
     * 父会话ID，用于关联任务上下文
     */
    @FieldDoc(description = "父会话ID，用于关联任务上下文", example = {})
    @ThriftField(1)
    private String parentSessionId;

    /**
     * 任务描述，详细说明需要Agent执行的任务内容
     */
    @FieldDoc(description = "任务描述，详细说明需要Agent执行的任务内容", example = {})
    @ThriftField(2)
    private String taskDesc;

    /**
     * Agent标识码，指定执行任务的Agent类型
     */
    @FieldDoc(description = "Agent标识码，指定执行任务的Agent类型", example = {})
    @ThriftField(3)
    private String agentCode;

    /**
     * 用户ID，任务发起者标识
     */
    @FieldDoc(description = "用户ID，任务发起者标识", example = {})
    @ThriftField(4)
    private String misId;

    /**
     * 任务来源，指定任务的发起源
     */
    @FieldDoc(description = "任务来源，指定任务的发起源", example = {})
    @ThriftField(5)
    private String source;

    /**
     * 确认回调文本，用于22628模板的确认按钮显示文本
     */
    @FieldDoc(description = "确认回调文本，用于22628模板的确认按钮显示文本", example = {})
    @ThriftField(6)
    private String confirmCallback;
}
