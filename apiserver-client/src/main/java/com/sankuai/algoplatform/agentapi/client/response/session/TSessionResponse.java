package com.sankuai.algoplatform.agentapi.client.response.session;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentSessionDTO;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月11日 15:01
 */
@ThriftStruct
@Data
public class TSessionResponse extends TBaseReponse {
    @FieldDoc(description = "session列表", example = {})
    @ThriftField(3)
    AgentSessionDTO data;

    public static TSessionResponse success(AgentSessionDTO agentSessionDTO) {
        TSessionResponse tSessionResponse = new TSessionResponse();
        tSessionResponse.setData(agentSessionDTO);
        tSessionResponse.setCode(200);
        return tSessionResponse;
    }

    public static TSessionResponse fail(String message) {
        TSessionResponse tSessionResponse = new TSessionResponse();
        tSessionResponse.setMessage(message);
        tSessionResponse.setCode(500);
        return tSessionResponse;
    }
}
