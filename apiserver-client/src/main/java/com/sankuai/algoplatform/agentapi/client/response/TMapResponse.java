package com.sankuai.algoplatform.agentapi.client.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.Map;

@ThriftStruct
@Data
public class TMapResponse extends  TBaseReponse {
    @FieldDoc(description = "返回信息", example = {})
    @ThriftField(3)
    Map<String, String> data;

    public static TMapResponse success(Map<String, String> data) {
        TMapResponse tMapResponse = new TMapResponse();
        tMapResponse.setCode(200);
        tMapResponse.setData(data);
        return tMapResponse;
    }

    public static TMapResponse fail(Integer code, String message) {
        TMapResponse tMapResponse = new TMapResponse();
        tMapResponse.setCode(code);
        tMapResponse.setMessage(message);
        return tMapResponse;
    }
}
