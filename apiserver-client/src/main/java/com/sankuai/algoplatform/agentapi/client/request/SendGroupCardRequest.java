package com.sankuai.algoplatform.agentapi.client.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.Map;

/**
 * 发送群组卡片请求对象
 *
 * <AUTHOR>
 * @date 2025年01月14日
 */
@ThriftStruct
@Data
public class SendGroupCardRequest {

    /**
     * 模版标识id
     */
    @FieldDoc(description = "模版标识id", example = {})
    @ThriftField(1)
    private Long templateId;

    /**
     * 群组id
     */
    @FieldDoc(description = "群组id", example = {})
    @ThriftField(2)
    private Long gid;

    /**
     * 摘要
     */
    @FieldDoc(description = "摘要", example = {})
    @ThriftField(3)
    private String abstractText;

    /**
     * sessionId
     */
    @FieldDoc(description = "sessionId", example = {})
    @ThriftField(4)
    private String sessionId;

    /**
     * 变量（txt、confirmCallback）
     */
    @FieldDoc(description = "变量（txt、confirmCallback）", example = {})
    @ThriftField(5)
    private Map<String, String> variableValue;
}

