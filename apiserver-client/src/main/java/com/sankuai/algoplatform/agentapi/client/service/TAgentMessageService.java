package com.sankuai.algoplatform.agentapi.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.dto.AgentOriginalMessageDTO;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.TStringResponse;
import com.sankuai.algoplatform.agentapi.client.response.message.TMessagesResponse;

import java.util.List;

/**
 * Agent消息服务接口
 * <AUTHOR>
 * @date 2025年05月25日 14:35
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "Agent会话消息接口", scenarios = "")
public interface TAgentMessageService {
    /**
     * 获取会话历史消息
     *
     * @param misId 用户ID
     * @param sessionId 会话ID
     * @return 消息列表
     */
    @ThriftMethod
    @MethodDoc(description = "获取会话历史消息")
    TMessagesResponse getSessionHistory(String misId, String sessionId, String startMsgId, Integer pageSize);

    /**
     * 保存单条消息
     *
     * @param agentMessageDTO 消息DTO
     * @return 是否成功
     */
    @ThriftMethod
    @MethodDoc(description = "保存单条消息")
    TCheckResponse saveSingleMessage(AgentMessageDTO agentMessageDTO);

    /**
     * 批量保存消息
     *
     * @param messageDTOS 消息DTO列表
     * @return 是否成功
     */
    @ThriftMethod
    @MethodDoc(description = "批量保存消息")
    TCheckResponse saveMessages(List<AgentMessageDTO> messageDTOS);

    /**
     * 保存单条原始消息
     *
     * @param messageDTO 原始消息DTO
     * @return 是否成功
     */
    @ThriftMethod
    @MethodDoc(description = "保存单条原始消息")
    TCheckResponse saveSingleOriginalMessage(AgentOriginalMessageDTO messageDTO);

    /**
     * 批量保存原始消息
     *
     * @param messageDTOS 原始消息DTO列表
     * @return 是否成功
     */
    @ThriftMethod
    @MethodDoc(description = "批量保存原始消息")
    TCheckResponse saveOriginalMessages(List<AgentOriginalMessageDTO> messageDTOS);

    /**
     * 获取解析数据
     *
     * @param sessionId 会话ID
     * @return 消息列表
     */
    @ThriftMethod
    @MethodDoc(description = "获取解析数据")
    TStringResponse getAnalysisData(String sessionId);
}
