package com.sankuai.algoplatform.agentapi.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.request.AllocateTaskForAgentRequest;
import com.sankuai.algoplatform.agentapi.client.response.TMapResponse;

@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "Agent任务管理接口", scenarios = "")
public interface TAgentTaskService {
    /**
     * 给Agent委派任务
     *
     * @param request 委派任务请求对象
     * @return Agent状态信息响应
     */
    @ThriftMethod
    @MethodDoc(description = "给Agent委派任务")
    TMapResponse allocateTaskForAgent(AllocateTaskForAgentRequest request);
}
