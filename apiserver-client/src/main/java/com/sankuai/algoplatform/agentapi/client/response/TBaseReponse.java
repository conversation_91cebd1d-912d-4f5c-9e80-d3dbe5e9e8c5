package com.sankuai.algoplatform.agentapi.client.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年06月09日 11:34
 */
@ThriftStruct
@Data
public class TBaseReponse {
    @FieldDoc(description = "状态码", example = {})
    @ThriftField(1)
    public Integer code;

    @FieldDoc(description = "错误信息", example = {})
    @ThriftField(2)
    public String message;

    /**
     * 创建成功响应
     * @return 成功的响应对象
     */
    public static TBaseReponse success() {
        TBaseReponse response = new TBaseReponse();
        response.setCode(0);
        response.setMessage("success");
        return response;
    }

    /**
     * 创建成功响应
     * @param message 自定义成功消息
     * @return 成功的响应对象
     */
    public static TBaseReponse success(String message) {
        TBaseReponse response = new TBaseReponse();
        response.setCode(0);
        response.setMessage(message);
        return response;
    }

    /**
     * 创建失败响应
     * @param code 错误码
     * @param message 错误信息
     * @return 失败的响应对象
     */
    public static TBaseReponse fail(Integer code, String message) {
        TBaseReponse response = new TBaseReponse();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    /**
     * 创建失败响应，使用默认错误码-1
     * @param message 错误信息
     * @return 失败的响应对象
     */
    public static TBaseReponse fail(String message) {
        return fail(-1, message);
    }

    /**
     * 参数错误
     * @param  message 错误信息
     * @return 失败的响应对象
     */
    public static TBaseReponse paramsFail(String message) {
        return fail(400, message);
    }

    /**
     * 鉴权失败
     * @param message
     * @return 鉴权失败的响应对象
     */
    public static TBaseReponse authFail(String message) {
        return fail(413, message);
    }

    /**
     * 鉴权失败
     * @return 鉴权失败的响应对象
     */
    public static TBaseReponse authFail() {
        return fail(413, "鉴权失败");
    }
}
