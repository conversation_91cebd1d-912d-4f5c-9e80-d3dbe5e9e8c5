package com.sankuai.algoplatform.agentapi.client.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年06月09日 17:58
 */
@ThriftStruct
@Data
public class TStringResponse extends TBaseReponse{
    @FieldDoc(description = "返回信息", example = {})
    @ThriftField(3)
    String data;

    public static TStringResponse success(String sessionId) {
        TStringResponse tStringResponse = new TStringResponse();
        tStringResponse.setCode(200);
        tStringResponse.setData(sessionId);
        return tStringResponse;
    }

    public static TStringResponse fail(String message) {
        TStringResponse tStringResponse = new TStringResponse();
        tStringResponse.setMessage(message);
        tStringResponse.setCode(500);
        return tStringResponse;
    }
}
