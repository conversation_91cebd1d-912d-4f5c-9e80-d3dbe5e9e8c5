package com.sankuai.algoplatform.agentapi.client.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/25
 */
@ThriftStruct
@Data
public class TKnowledgeCollectRequest {
    @FieldDoc(description = "agentCode", example = {})
    @ThriftField(1)
    public String agentCode;

    @FieldDoc(description = "knowledgeKey", example = {})
    @ThriftField(2)
    public Long knowledgeKey;

    @FieldDoc(description = "知识数据, 复合字段", example = {})
    @ThriftField(3)
    public String knowledgeData;

    @FieldDoc(description = "用于生成知识的源数据Id", example = {})
    @ThriftField(4)
    public Long collectId;
}