package com.sankuai.algoplatform.agentapi.client.enums;

/**
 * <AUTHOR>
 * @date 2025年06月09日 18:24
 */
public enum ResponseCodeEnum {
    SUCCESS(200, "成功");


    Integer code;
    String desc;

    ResponseCodeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
