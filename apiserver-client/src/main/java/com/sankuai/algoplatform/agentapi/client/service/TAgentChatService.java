package com.sankuai.algoplatform.agentapi.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.response.TStringResponse;
import com.sankuai.algoplatform.agentapi.client.response.chat.TChatResponse;

import java.util.Map;

/**
 * Agent聊天服务接口
 * <AUTHOR>
 * @date 2025年05月28日
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "Agent聊天服务接口", scenarios = "")
public interface TAgentChatService {
    /**
     * 发送聊天消息
     * @param misId 用户ID
     * @param agentMessageDTO 消息DTO
     * @return 响应结果
     */
    @ThriftMethod
    @MethodDoc(description = "发送聊天消息")
    TChatResponse chat(String misId, AgentMessageDTO agentMessageDTO);

    /**
     * 回调接口，传输信息给 agent
     * @param source 消息来源
     * @param sessionId 会话ID
     * @param content 消息内容
     * @return 响应结果
     */
    @ThriftMethod
    @MethodDoc(description = "回调接口，传输信息给 agent")
    TChatResponse trans2agent(String source, String sessionId, Map<String, String> content);
}
