package com.sankuai.algoplatform.agentapi.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentNodeFlowDTO;
import com.sankuai.algoplatform.agentapi.client.response.Response;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import com.sankuai.algoplatform.agentapi.client.response.node.TNodesResponse;

import java.util.List;

/**
 * Agent节点流转服务接口
 * <AUTHOR>
 * @date 2025年05月25日 14:35
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "Agent节点流转接口", scenarios = "")
public interface TAgentNodeFlowService {

    /**
     * 保存单条节点流转记录
     *
     * @param nodeFlowDTO 节点流转DTO
     * @return 保存结果
     */
    @ThriftMethod
    @MethodDoc(description = "保存单条节点流转记录")
    TCheckResponse saveSingleNodeFlow(AgentNodeFlowDTO nodeFlowDTO);

    /**
     * 批量保存节点流转记录
     *
     * @param nodeFlowDTOList 节点流转DTO列表
     * @return 保存结果
     */
    @ThriftMethod
    @MethodDoc(description = "批量保存节点流转记录")
    TCheckResponse batchSaveNodeFlow(List<AgentNodeFlowDTO> nodeFlowDTOList);

    /**
     * 根据会话ID查询节点流转记录
     *
     * @param sessionId 会话ID
     * @return 节点流转记录列表
     */
    @ThriftMethod
    @MethodDoc(description = "根据会话ID查询节点流转记录")
    TNodesResponse listNodeFlowBySessionId(String sessionId);

    /**
     * 根据消息ID查询节点流转记录
     *
     * @param msgId 消息ID
     * @return 节点流转记录列表
     */
    @ThriftMethod
    @MethodDoc(description = "根据消息ID查询节点流转记录")
    Response<List<AgentNodeFlowDTO>> listNodeFlowByMsgId(String msgId);

    /**
     * 根据会话ID和消息ID查询节点流转记录
     *
     * @param sessionId 会话ID
     * @param msgId 消息ID
     * @return 节点流转记录列表
     */
    @ThriftMethod
    @MethodDoc(description = "根据会话ID和消息ID查询节点流转记录")
    TNodesResponse listNodeFlowBySessionIdAndMsgId(String sessionId, String msgId);

    /**
     * 根据会话ID和当前节点名称查询节点流转记录
     *
     * @param sessionId 会话ID
     * @param currentNodeName 当前节点名称
     * @return 节点流转记录列表
     */
    @ThriftMethod
    @MethodDoc(description = "根据会话ID和当前节点名称查询节点流转记录")
    TNodesResponse listNodeFlowByCurrentNodeName(String sessionId, String currentNodeName);

}
