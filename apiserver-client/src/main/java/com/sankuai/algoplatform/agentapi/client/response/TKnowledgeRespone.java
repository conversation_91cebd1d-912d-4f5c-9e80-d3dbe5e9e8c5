package com.sankuai.algoplatform.agentapi.client.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/7/6
 */
@ThriftStruct
@Data
public class TKnowledgeRespone extends TBaseReponse{
    @FieldDoc(description = "请求状态", example = {})
    @ThriftField(3)
    public Map<String,String> data;

    public static TKnowledgeRespone success(Map<String,String> data) {
        TKnowledgeRespone tKnowledgeRespone = new TKnowledgeRespone();
        tKnowledgeRespone.setMessage("success");
        tKnowledgeRespone.setCode(200);
        tKnowledgeRespone.setData(data);
        return tKnowledgeRespone;
    }
    public static TKnowledgeRespone fail(String message) {
        TKnowledgeRespone tKnowledgeRespone = new TKnowledgeRespone();
        tKnowledgeRespone.setMessage(message);
        tKnowledgeRespone.setCode(-1);
        return tKnowledgeRespone;
    }



}
