package com.sankuai.algoplatform.agentapi.client.response.chat;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import com.sankuai.algoplatform.agentapi.client.response.TCheckResponse;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年06月09日 20:03
 */
@ThriftStruct
@Data
public class TChatResponse extends TBaseReponse {
    @FieldDoc(description = "消息列表", example = {})
    @ThriftField(3)
    Map<String, String> data;

    public static TChatResponse success(Map<String, String> data) {
        TChatResponse tChatResponse = new TChatResponse();
        tChatResponse.setCode(200);
        tChatResponse.setData(data);
        return tChatResponse;
    }

    public static TChatResponse fail(Integer code, String message) {
        TChatResponse tChatResponse = new TChatResponse();
        tChatResponse.setCode(code);
        tChatResponse.setMessage(message);
        return tChatResponse;
    }
}
