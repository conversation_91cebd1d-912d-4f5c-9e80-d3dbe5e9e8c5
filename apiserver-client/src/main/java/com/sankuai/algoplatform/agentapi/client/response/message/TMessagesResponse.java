package com.sankuai.algoplatform.agentapi.client.response.message;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.dto.PageInfo;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月09日 17:59
 */
@ThriftStruct
@Data
public class TMessagesResponse extends TBaseReponse {
    @FieldDoc(description = "消息列表", example = {})
    @ThriftField(3)
    List<AgentMessageDTO> data;

    @FieldDoc(description = "分页信息", example = {})
    @ThriftField(4)
    PageInfo pageInfo;
}
