package com.sankuai.algoplatform.agentapi.client.response;

import com.sankuai.algoplatform.agentapi.client.dto.PageInfo;

import java.io.Serializable;

/**
 * 通用响应类，包含状态和分页信息
 * <AUTHOR>
 * @date 2025年05月26日 11:39
 */
public class Response<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 状态信息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 分页信息
     */
    private PageInfo pageInfo;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 默认构造函数
     */
    public Response() {
    }

    /**
     * 构造函数
     * @param code 状态码
     * @param message 状态信息
     * @param data 响应数据
     */
    public Response(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = code == 200;
    }

    /**
     * 构造函数
     * @param code 状态码
     * @param message 状态信息
     * @param data 响应数据
     * @param pageInfo 分页信息
     */
    public Response(Integer code, String message, T data, PageInfo pageInfo) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.pageInfo = pageInfo;
        this.success = code == 200;
    }

    /**
     * 创建成功响应
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> Response<T> success(T data) {
        return new Response<>(200, "操作成功", data);
    }

    /**
     * 创建成功响应（带分页）
     * @param data 响应数据
     * @param pageInfo 分页信息
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> Response<T> success(T data, PageInfo pageInfo) {
        return new Response<>(200, "操作成功", data, pageInfo);
    }

    /**
     * 创建失败响应
     * @param code 状态码
     * @param message 状态信息
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> Response<T> fail(Integer code, String message) {
        return new Response<>(code, message, null);
    }

    /**
     * 创建失败响应（默认状态码500）
     * @param message 状态信息
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> Response<T> fail(String message) {
        return new Response<>(500, message, null);
    }

    // Getters and Setters
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
