package com.sankuai.algoplatform.agentapi.client.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.algoplatform.agentapi.client.request.SendGroupCardRequest;
import com.sankuai.algoplatform.agentapi.client.request.SendMisCardRequest;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;

import java.util.List;
import java.util.Map;

/**
 * 大象卡片推送服务接口
 *
 * <AUTHOR>
 * @date 2025年05月28日
 */
@ThriftService
@InterfaceDoc(type = "octo.thrift.annotation", description = "大象推送服务接口", scenarios = "")
public interface TOpenCardService {
    /**
     * @param templateId    模版标识id
     * @param misList       mis列表
     * @param abstractText  摘要
     * @param variableValue 变量（txt、confirmCallback）
     */
    @ThriftMethod
    @MethodDoc(description = "发送聊天消息")
    TBaseReponse sendMisCardWithoutSessionId(Long templateId, List<String> misList, String abstractText, Map<String, String> variableValue);

    /**
     * 发送MIS卡片消息（使用Request对象）
     * @param request 发送MIS卡片请求对象
     */
    @ThriftMethod
    @MethodDoc(description = "发送聊天消息")
    TBaseReponse sendMisCard(SendMisCardRequest request);

    /**
     * 发送群组卡片消息（使用Request对象）
     * @param request 发送群组卡片请求对象
     */
    @ThriftMethod
    @MethodDoc(description = "发送群聊消息")
    TBaseReponse sendGroupCard(SendGroupCardRequest request);
}
