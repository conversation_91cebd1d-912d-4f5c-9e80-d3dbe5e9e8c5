package com.sankuai.algoplatform.agentapi.starter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cip.crane.netty.utils.Pair;
import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.lion.client.util.StringUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.reflect.TypeToken;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsTextVectorBuildResponse;
import com.sankuai.algoplatform.agentapi.infrastructure.constant.LionConfig;
import com.sankuai.algoplatform.agentapi.infrastructure.util.ClassificationMetrics;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeLabeledSample;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeRecord;
import com.sankuai.algoplatform.agentapi.infrastructure.config.AgentKnowledgeConfig;
import com.sankuai.algoplatform.agentapi.infrastructure.dao.KnowLedgeRecordDao;
import com.sankuai.algoplatform.agentapi.infrastructure.dao.KnowledgeEvaluationDao;
import com.sankuai.algoplatform.agentapi.infrastructure.dao.KnowledgeLabeledSampleDao;
import com.sankuai.algoplatform.agentapi.infrastructure.entity.*;
import com.sankuai.algoplatform.agentapi.infrastructure.exceptions.ContinousLearningException;
import com.sankuai.algoplatform.agentapi.infrastructure.factory.BatchProcessor;
import com.sankuai.algoplatform.agentapi.infrastructure.factory.RateLimiterFactory;
import com.sankuai.algoplatform.agentapi.infrastructure.factory.ThreadPoolFactory;
import com.sankuai.algoplatform.agentapi.infrastructure.util.RetryUtil;
import com.sankuai.algoplatform.agentapi.starter.service.ContinousLearningService;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.sankuai.algoplatform.agentapi.infrastructure.constant.LionConfig.*;


/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */
@Service
@Slf4j
public class ContinousLearningServiceImpl implements ContinousLearningService {

    @Resource
    private KnowLedgeRecordDao knowledgeRecordDao;

    @Resource
    private KnowledgeEvaluationDao knowledgeEvaluationDao;

    @Resource(name = "TAgentChatService")
    private TPredictService.Iface tPredictService;

    @Resource
    private BatchProcessor batchProcessor;

    @Resource
    private KnowledgeLabeledSampleDao knowledgeLabeledSampleDao;

    @Resource
    private FuXiKnowledgeService fuXiKnowledgeService;

    @Resource
    private RateLimiterFactory rateLimiterFactory;

    private static final String F1_SCORE = "F1_Score";


    private static final ThreadPoolExecutor KNOWLEDGE_COLLECT_POOL = ThreadPoolFactory.createThreadPool(
            "knowledge-collect", 50, 128, 30, 200);

    private static final ThreadPoolExecutor KNOWLEDGE_EVAL_POOL = ThreadPoolFactory.createThreadPool(
            "knowledge-eval", 100, 128, 30, 200);


    @Override
    public void collectKnowledgePeriodically() {
        log.info("开始执行知识收集定时任务");
        try {
            Integer scanLimit = KNOWLEDGE_COLLECT_SCAN_NUM != null && KNOWLEDGE_COLLECT_SCAN_NUM > 0 ? KNOWLEDGE_COLLECT_SCAN_NUM : 1;
            // 每次定时任务只扫描指定数量的记录，实现分批处理
            List<KnowledgeRecord> knowledgeRecords = knowledgeRecordDao.selectByCollectStatusZero(scanLimit);
            if (CollectionUtils.isEmpty(knowledgeRecords)) {
                Cat.logEvent("collectKnowledgePeriodically", "noRecord");
                log.info("没有需要收集的知识记录, 本次定时任务结束, time: {}", Date.from(Instant.now()).toLocaleString());
                return;
            }

            log.info("本次扫描到待收集的知识记录: {} 条 (扫描限制: {} 条)", knowledgeRecords.size(), scanLimit);

            // 先批量更新状态为收集中，防止重复处理
            BatchUpdateResult updateResult = knowledgeRecordDao.updateBatchCollectStatus(
                    knowledgeRecords, KnowledgeCollectStatusEnum.COLLECTING.getCode());
            log.info("批量更新记录状态完成 - 总数: {}, 成功: {}, 失败: {}", updateResult.getTotalCount(), updateResult.getSuccessCount(), updateResult.getFailureCount());

            BatchProcessConfig config = BatchProcessConfig.builder()
                    .threadPool(KNOWLEDGE_COLLECT_POOL)
                    .concurrentSize(knowledgeRecords.size()) // 并发数等于扫描到的记录数
                    .batchName("知识收集")
                    .enableDetailLog(true)
                    .build();

            log.info("开始并发处理知识收集 - 并发数: {}",
                    knowledgeRecords.size(), "KNOWLEDGE_COLLECT_POOL");

            BatchProcessResult<KnowledgeRecord, Boolean> result = batchProcessor.processBatch(
                    knowledgeRecords,
                    this::processKnowledgeRecord,
                    config
            );

            log.info("知识收集定时任务执行完成 - 处理记录数: {}, 成功: {}, 失败: {}, 成功率: {}%",
                    knowledgeRecords.size(), result.getSuccessCount(), result.getFailureCount(),
                    result.getSuccessRate() * 100);

            Cat.logMetricForCount("KnowledgeCollect.ProcessCount", result.getTotalCount());
            Cat.logMetricForCount("KnowledgeCollect.SuccessCount", result.getSuccessCount());
            Cat.logMetricForCount("KnowledgeCollect.FailureCount", result.getFailureCount());

        } catch (Exception e) {
            log.error("知识收集定时任务执行失败", e);
        }
    }

    @SneakyThrows
    @Override
    public void monitorExperienceUpdate() {
        try {
            List<KnowledgeEvaluation> knowledgeEvaluations = knowledgeEvaluationDao.selectByEvalStatus(KnowledgeEvalStatusEnum.NEW_CREATED.getCode(), KNOWLEDGE_EVAL_SCAN_LIMIT);
            if (CollectionUtils.isEmpty(knowledgeEvaluations)) {
                log.info("没有需要更新的知识");
                return;
            }
            knowledgeEvaluationDao.updateBatchEvalStatus(knowledgeEvaluations, KnowledgeEvalStatusEnum.EVALUATING.getCode());
            log.info("本次需要处理的知识记录总数: {}", knowledgeEvaluations.size());
            List<ExecuteEvaluationContext> executeEvaluationContexts = new ArrayList<>();
            for (KnowledgeEvaluation knowledgeEvaluation : knowledgeEvaluations) {
                String agentCode = knowledgeEvaluation.getAgentCode();
                AgentKnowledgeConfig agentKnowledgeConfig = AGENT_KNOWLEDGE_MAP.get(agentCode);
                List<KnowledgeLabeledSample> knowledgeLabeledSamples = knowledgeLabeledSampleDao.selectAllNewPartitionDateSample(agentCode, agentKnowledgeConfig.getSampleLimt());
                if (CollectionUtils.isEmpty(knowledgeLabeledSamples)) {
                    Cat.logEvent("knowledgeLabeledSamples", "empty");
                    continue;
                }
                ExecuteEvaluationContext executeEvaluationContext = new ExecuteEvaluationContext();
                executeEvaluationContext.setKnowledgeEvaluation(knowledgeEvaluation);
                executeEvaluationContext.setKnowledgeLabeledSamples(knowledgeLabeledSamples);
                executeEvaluationContext.setAgentKnowledgeConfig(agentKnowledgeConfig);
                executeEvaluationContext.setItems(createExecutionAssessmentMinUnit(executeEvaluationContext));
                executeEvaluationContexts.add(executeEvaluationContext);
            }
            asyncExecuteEval(executeEvaluationContexts);
        } catch (Exception e) {
            log.error("知识更新定时任务执行失败", e);
        }
    }

    private void asyncExecuteEval(List<ExecuteEvaluationContext> executeEvaluationContexts) {
        for (ExecuteEvaluationContext executeEvaluationContext : executeEvaluationContexts) {
            List<ExecuteEvaluationContext.ExecuteEvaluationContextItem> items = executeEvaluationContext.getItems();
            AgentKnowledgeConfig agentKnowledgeConfig = executeEvaluationContext.getAgentKnowledgeConfig();
            Integer evalConcurrentSize = agentKnowledgeConfig.getEvalBatchSize();
            Integer maxRequestsPerMinute = agentKnowledgeConfig.getMaxRequestsPerMinute();

            log.info("本次并发数: {}, 限流配置: {}请求/分钟", evalConcurrentSize, maxRequestsPerMinute);

            // 构建批处理配置
            BatchProcessConfig.BatchProcessConfigBuilder configBuilder = BatchProcessConfig.builder()
                    .threadPool(KNOWLEDGE_EVAL_POOL)
                    .concurrentSize(evalConcurrentSize)
                    .batchName("知识测评")
                    .enableDetailLog(true);

            // 如果配置了限流，则创建限流器
            if (maxRequestsPerMinute != null && maxRequestsPerMinute > 0) {
                try {
                    com.google.common.util.concurrent.RateLimiter rateLimiter = rateLimiterFactory.createRateLimiter(maxRequestsPerMinute);
                    configBuilder.maxRequestsPerMinute(maxRequestsPerMinute)
                            .rateLimiter(rateLimiter);
                    log.info("已启用限流控制: {}请求/分钟", maxRequestsPerMinute);
                } catch (Exception e) {
                    log.warn("创建限流器失败，将不使用限流: {}", e.getMessage());
                }
            } else {
                log.info("未配置限流或限流值无效，不启用限流控制");
            }

            BatchProcessConfig config = configBuilder.build();

            // 并发处理所有评估项目
            BatchProcessResult<ExecuteEvaluationContext.ExecuteEvaluationContextItem, Pair<Integer, Integer>> itemResults =
                    batchProcessor.processBatch(items, this::processItemEval, config);

            log.info("知识测评定时任务执行完成，处理结果：总数: {}, 成功: {}, 失败: {}, 成功率: {}",
                    itemResults.getTotalCount(), itemResults.getSuccessCount(), itemResults.getFailureCount(),
                    itemResults.getSuccessRate() * 100);
            Cat.logMetricForCount("KnowledgeEval.ProcessCount", itemResults.getTotalCount(), ImmutableMap.of("agentCode", executeEvaluationContext.getKnowledgeEvaluation().getAgentCode(), "evalTask", agentKnowledgeConfig.getEvalTask()));
            Cat.logMetricForCount("KnowledgeEval.SuccessCount", itemResults.getSuccessCount());
            Cat.logMetricForCount("KnowledgeEval.FailureCount", itemResults.getFailureCount());

            List<Long> failedSampleIds = extractFailedSampleIds(itemResults);

            List<Pair<Integer, Integer>> evaluationPairs = new ArrayList<>();
            for (BatchProcessResult.ItemProcessResult<ExecuteEvaluationContext.ExecuteEvaluationContextItem, Pair<Integer, Integer>> successResult : itemResults.getSuccessResults()) {
                Pair<Integer, Integer> result = successResult.getResult();
                evaluationPairs.add(result);
            }
            executeEvalProcess(executeEvaluationContext, evaluationPairs, failedSampleIds);
        }

    }

    private void executeEvalProcess(ExecuteEvaluationContext context, List<Pair<Integer, Integer>> evaluationPairs, List<Long> failedSampleIds) {
        KnowledgeEvaluation evaluation = context.getKnowledgeEvaluation();
        Long evaluationId = evaluation.getId();
        AgentKnowledgeConfig agentKnowledgeConfig = context.getAgentKnowledgeConfig();
        log.info("开始计算评估指标, id: {}, collectId: {}, agentCode: {}", evaluation.getId(), evaluation.getCollectId(), evaluation.getAgentCode());
        try {
            Cat.logMetricForCount("KnowledgeEval.EvalCount", 1, ImmutableMap.of("agentCode", evaluation.getAgentCode(), "evalTask", agentKnowledgeConfig.getEvalTask()));
            // 获取该评估的所有结果
            if (evaluationPairs.isEmpty()) {
                log.warn("评估记录 {} 没有有效的评估结果", evaluationId);
                throw new ContinousLearningException("没有有效的评估结果");
            }

            // 分离预测值和真实值
            List<Integer> yTrueList = new ArrayList<>();
            List<Integer> yPredList = new ArrayList<>();
            for (Pair<Integer, Integer> pair : evaluationPairs) {
                yPredList.add(pair.getFirst());  // 预测值
                yTrueList.add(pair.getSecond()); // 真实值
            }
            log.info("yTrueList数量: {}, yPredList数量: {}", yTrueList.size(), yPredList.size());
            // 计算评估指标
            Map<String, Double> evalData = ClassificationMetrics.calculateBinaryMetrics(yTrueList, yPredList);
            String evalReport = JSONObject.toJSONString(evalData);

            // 根据F1值判断是否通过
            List<KnowledgeLabeledSample> knowledgeLabeledSamples = context.getKnowledgeLabeledSamples();

            if (failedSampleIds != null && !failedSampleIds.isEmpty()) {
                int originalSize = knowledgeLabeledSamples.size();

                // 创建新的过滤后列表，避免并发修改问题
                knowledgeLabeledSamples = knowledgeLabeledSamples.stream()
                        .filter(sample -> !failedSampleIds.contains(sample.getSampleId()))
                        .collect(Collectors.toList());

                int filteredSize = knowledgeLabeledSamples.size();
                log.info("过滤失败样本完成 - 原始样本数: {}, 过滤后样本数: {}, 被过滤样本数: {}",
                        originalSize, filteredSize, originalSize - filteredSize);

                if (knowledgeLabeledSamples.isEmpty()) {
                    log.warn("过滤后没有有效样本用于计算在线F1分数, evaluationId: {}", evaluationId);
                    throw new ContinousLearningException("过滤失败样本后，没有有效样本进行评估");
                }
            }

            Double onlineF1Score = ClassificationMetrics.caculeteOnlineF1Score(knowledgeLabeledSamples);
            Double testF1Score = evalData.get(F1_SCORE);
            Boolean isNegative = testF1Score < onlineF1Score;
            Integer evalResult = 0;
            if (isNegative) {
                evalResult = KnowledgeEvalResultEnum.NEGATIVE.getCode();
                Cat.logMetricForCount("KnowledgeEval.NegativeCount", 1, ImmutableMap.of("agentCode", evaluation.getAgentCode(), "evalTask", agentKnowledgeConfig.getEvalTask()));
            } else {
                evalResult = KnowledgeEvalResultEnum.POSITIVE.getCode();
                Cat.logMetricForCount("KnowledgeEval.PositiveCount", 1, ImmutableMap.of("agentCode", evaluation.getAgentCode(), "evalTask", agentKnowledgeConfig.getEvalTask()));
            }
            log.info("计算完毕，结论如下：evaluationId:{}, agentCode: {}, 标注样本的F1值: {}, 新增知识的F1值: {}, isNegative: {},evalData:{}", evaluationId, evaluation.getAgentCode(), onlineF1Score, testF1Score, isNegative, JSONObject.toJSONString(evalData));
            // 处理知识库操作
            if (evalResult == KnowledgeEvalResultEnum.NEGATIVE.getCode()) {
                // 删除负向知识
                DsTextVectorBuildResponse dsTextVectorBuildResponse = fuXiKnowledgeService.textVectorDelete(
                        evaluation.getKnowledgeData(), agentKnowledgeConfig.getAssKnowBaseId());
                if (dsTextVectorBuildResponse.getSuccess()) {
                    log.info("新知识未通过测评，已在知识库中删除！,docId:{} knowledgeData: {}", evaluation.getKnowledgeKey(), evaluation.getKnowledgeData());
                } else {
                    throw new ContinousLearningException("删除知识失败！:" + dsTextVectorBuildResponse.getMsg() + " 知识内容:" + evaluation.getKnowledgeData());
                }
            } else {
                // 同步正向知识到正式知识库
                if (agentKnowledgeConfig.getSyncToFormalKb()) {
                    DsTextVectorBuildResponse dsTextVectorBuildResponse = fuXiKnowledgeService.textVectorBuild(
                            evaluation.getKnowledgeData(), agentKnowledgeConfig.getKnowBaseId());
                    if (dsTextVectorBuildResponse.getSuccess()) {
                        log.info("新知识通过测评，已同步到正式知识库！,docId:{} knowledgeData: {}", evaluation.getKnowledgeKey(), evaluation.getKnowledgeData());
                    } else {
                        throw new ContinousLearningException("同步知识失败！:" + dsTextVectorBuildResponse.getMsg() + " 知识内容:" + evaluation.getKnowledgeData());
                    }
                }
            }
            // 更新评估状态
            evaluation.setEvalResult(evalResult);
            evaluation.setEvalStatus(KnowledgeEvalStatusEnum.COMPLETED.getCode());
            evaluation.setEvalReport(evalReport);
            knowledgeEvaluationDao.updateKnowledgeEvaluation(evaluation);
            log.info("知识评估成功, id: {}, collectId: {}, result: {}", evaluation.getId(), evaluation.getCollectId(), evalResult);
        } catch (Exception e) {
            log.error("知识评估失败, id: {}, error: {}", context.getKnowledgeEvaluation().getId(), e.getMessage(), e);
            Cat.logMetricForCount("KnowledgeEval.ErrorCount", 1);
            knowledgeEvaluationDao.updateEvalStatus(context.getKnowledgeEvaluation().getId(), KnowledgeEvalStatusEnum.FAILED.getCode());
        }
    }


    private List<ExecuteEvaluationContext.ExecuteEvaluationContextItem> createExecutionAssessmentMinUnit(ExecuteEvaluationContext executeEvaluationContext) {
        List<ExecuteEvaluationContext.ExecuteEvaluationContextItem> list = new ArrayList<>();
        KnowledgeEvaluation knowledgeEvaluation = executeEvaluationContext.getKnowledgeEvaluation();
        List<KnowledgeLabeledSample> knowledgeLabeledSamples = executeEvaluationContext.getKnowledgeLabeledSamples();
        for (KnowledgeLabeledSample knowledgeLabeledSample : knowledgeLabeledSamples) {
            ExecuteEvaluationContext.ExecuteEvaluationContextItem item = new ExecuteEvaluationContext.ExecuteEvaluationContextItem();
            item.setKnowledgeLabeledSample(knowledgeLabeledSample);
            item.setKnowledgeEvaluation(knowledgeEvaluation);
            list.add(item);
        }
        return list;
    }


    @SneakyThrows
    private Pair<Integer, Integer> processItemEval(ExecuteEvaluationContext.ExecuteEvaluationContextItem item) {
        KnowledgeEvaluation knowledgeEvaluation = item.getKnowledgeEvaluation();
        KnowledgeLabeledSample knowledgeLabeledSample = item.getKnowledgeLabeledSample();
        String evalTask = AGENT_KNOWLEDGE_MAP.get(knowledgeEvaluation.getAgentCode()).getEvalTask();
        try {
            Cat.logMetricForCount("KnowledgeEval.processItemEval.TotalCount", 1, ImmutableMap.of("agentCode", knowledgeEvaluation.getAgentCode(), "evalTask", evalTask));
            Pair<Integer, Integer> pair = new Pair<>();
            log.info("调用模型接口, id: {}, sampleId: {}, agentCode: {}, request: {}", knowledgeLabeledSample.getId(), knowledgeLabeledSample.getSampleId(), knowledgeLabeledSample.getAgentCode(), JSONObject.toJSONString(item));
            TPredictRequest tPredictRequest = buildTPredictRequest(knowledgeEvaluation, knowledgeLabeledSample);
            TPredictResponse tPredictResponse = tPredictService.predict(tPredictRequest);
            log.info("调用模型接口返回, id: {}, sampleId: {}, agentCode: {}, response: {}", knowledgeLabeledSample.getId(), knowledgeLabeledSample.getSampleId(), knowledgeLabeledSample.getAgentCode(), JSONObject.toJSONString(tPredictResponse));
            if (tPredictResponse.getCode() != 0 || tPredictResponse.getData() == null) {
                throw new ContinousLearningException("调用模型接口失败！");
            }
            String preResult = tPredictResponse.getData().get("risk_status");
            if (StringUtils.isBlank(preResult)) {
                throw new ContinousLearningException("调用模型接口返回结果为空！");
            }
            if (preResult.equals("0") || preResult.equals("1")) {
                pair.setFirst(Integer.valueOf(preResult));
                pair.setSecond(Integer.valueOf(knowledgeLabeledSample.getLabelResult()));
            } else {
                log.error("risk_status的值不是0或1,id:{},sampleId:{},risk_status:{}", knowledgeLabeledSample.getId(), knowledgeLabeledSample.getSampleId(), preResult);
                Cat.logEvent("riskStatusException", preResult);
                throw new ContinousLearningException("模型返回的risk_status值无效: " + preResult + ", 期望值为0或1");
            }
            Cat.logMetricForCount("KnowledgeEval.processItemEval.SuccessCount", 1, ImmutableMap.of("agentCode", knowledgeEvaluation.getAgentCode(), "evalTask", evalTask));
            log.info("知识评估成功, id: {}, sampleId: {}", knowledgeLabeledSample.getId(), knowledgeLabeledSample.getSampleId());
            return pair;
        } catch (Exception e) {
            Cat.logMetricForCount("KnowledgeEval.processItemEval.FailedCount", 1, ImmutableMap.of("agentCode", knowledgeEvaluation.getAgentCode(), "evalTask", evalTask));
            log.error("知识评估失败！processItemEval failed, evaluationId={}, sampleId={}, finalError: {}", knowledgeEvaluation.getId(), knowledgeLabeledSample.getId(), e.getMessage());
            return null;
        }

    }

    /**
     * 处理单条知识记录（同步处理方式，避免线程池嵌套）
     *
     * @param record 知识记录
     * @return 处理结果
     */
    public Boolean processKnowledgeRecord(KnowledgeRecord record) {
        log.info("开始处理知识记录, id: {}, collectId: {}, agentCode: {}",
                record.getId(), record.getCollectId(), record.getAgentCode());
        try {
            // 同步调用外部服务获取经验
            Boolean requestSuccess = sendAsyncExperienceRequest(record);

            // 根据调用结果更新最终状态
            Integer finalStatus = requestSuccess ?
                    KnowledgeCollectStatusEnum.COMPLETED.getCode() :
                    KnowledgeCollectStatusEnum.FAILED.getCode();

            Integer updateCount = knowledgeRecordDao.updateCollectStatus(
                    record.getId(),
                    record.getPartitionDate(),
                    finalStatus
            );
            log.info("知识记录处理完成, id: {}, collectId: {}, success: {}, finalStatus: {}",
                    record.getId(), record.getCollectId(), requestSuccess, finalStatus);
            return updateCount > 0;
        } catch (Exception e) {
            log.error("处理知识记录失败, id: {}, collectId: {}, error: {}", record.getId(), record.getCollectId(), e.getMessage(), e);
            knowledgeRecordDao.updateCollectStatus(record.getId(), record.getPartitionDate(), KnowledgeCollectStatusEnum.FAILED.getCode());
            return false;
        }
    }

    /**
     * 发送经验获取请求（同步调用，避免线程池嵌套）
     *
     * @param record 知识记录
     * @return 请求发送是否成功
     */
    private Boolean sendAsyncExperienceRequest(KnowledgeRecord record) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("sendAsyncExperienceRequest start, input={}, recordId: {}, collectId: {}", JSONObject.toJSONString(record), record.getId(), record.getCollectId());
            TPredictRequest tPredictRequest = buildTPredictRequest(record);
            long callStartTime = System.currentTimeMillis();
            log.info("开始调用外部服务, recordId: {}, collectId: {}, 开始时间: {}",
                    record.getId(), record.getCollectId(), callStartTime);

            TPredictResponse predictResult = tPredictService.predict(tPredictRequest);

            long callEndTime = System.currentTimeMillis();
            long callDuration = callEndTime - callStartTime;
            log.info("外部服务调用完成, recordId: {}, collectId: {}, 耗时: {}ms, response={}",
                    record.getId(), record.getCollectId(), callDuration, JSONObject.toJSONString(predictResult));

            if (predictResult == null || predictResult.getCode() != 0) {
                String errorCode = predictResult != null ? String.valueOf(predictResult.getCode()) : "null";
                String errorMsg = predictResult != null && predictResult.getData() != null ?
                        predictResult.getData().toString() : "无响应数据";
                log.error("调用模型服务失败, recordId: {}, collectId: {}, 返回码: {}, 错误信息: {}, 耗时: {}ms",
                        record.getId(), record.getCollectId(), errorCode, errorMsg, callDuration);
                throw new ContinousLearningException("模型服务调用失败，返回码: " + errorCode + ", 错误: " + errorMsg);
            }
            log.info("外部服务返回详情, recordId: {}, collectId: {}, output: {}, front_output: {}, 耗时: {}ms",
                    record.getId(), record.getCollectId(), predictResult.getData().get("output"),
                    predictResult.getData().get("front_output"), callDuration);
            return true;
        } catch (Exception e) {
            long totalDuration = System.currentTimeMillis() - startTime;
            log.error("发送经验获取请求失败, input={}, error: {}, 总耗时: {}ms",
                    JSONObject.toJSONString(record), e.getMessage(), totalDuration);
            return false;
        }
    }


    private TPredictRequest buildTPredictRequest(KnowledgeEvaluation knowledgeEvaluation, KnowledgeLabeledSample knowledgeLabeledSample) {
        TPredictRequest tPredictRequest = new TPredictRequest();
        String featureData = knowledgeLabeledSample.getFeatureData();
        if (StringUtils.isBlank(featureData)) {
            return null;
        }
        tPredictRequest.setBizCode(knowledgeEvaluation.getAgentCode());
        AgentKnowledgeConfig agentKnowledgeConfig = AGENT_KNOWLEDGE_MAP.get(knowledgeEvaluation.getAgentCode());
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        Map<String, String> req = JSONObject.parseObject(featureData, type);
        req.put("task", agentKnowledgeConfig.getEvalTask());
        req.put("data_stream_id", String.valueOf(agentKnowledgeConfig.getAssKnowBaseId()));
        req.put("api_key", agentKnowledgeConfig.getApiKey());
        tPredictRequest.setReq(req);
        return tPredictRequest;
    }

    private TPredictRequest buildTPredictRequest(KnowledgeRecord record) throws ContinousLearningException {
        try {
            TPredictRequest tPredictRequest = new TPredictRequest();
            AgentKnowledgeConfig agentKnowledgeConfig = AGENT_KNOWLEDGE_MAP.get(record.getAgentCode());
            String agentCode = record.getAgentCode();
            tPredictRequest.setBizCode(agentCode);
            String featureData = record.getFeatureData();
            Map<String, String> req = new HashMap<>();
            // 这里可能抛出 ContinousLearningException
            req.put("input_data", aviatorProcessRecord(agentKnowledgeConfig, featureData));
            req.put("task", agentKnowledgeConfig.getCreateKnowledgeTask());
            req.put("data_stream_id", String.valueOf(agentKnowledgeConfig.getKnowBaseId()));
            req.put("api_key", agentKnowledgeConfig.getApiKey());
            req.put("agent_code", record.getAgentCode());
            tPredictRequest.setReq(req);
            return tPredictRequest;
        } catch (ContinousLearningException e) {
            log.error("构建预测请求失败, recordId: {}, agentCode: {}, error: {}",
                    record.getId(), record.getAgentCode(), e.getMessage(), e);
            // 重新抛出异常，让上层处理
            throw e;
        }
    }
//
//    private String processNilAgentResult(String featureData) {
//        Map<String, String> map = JSONObject.parseObject(featureData, Map.class);
//        String result = map.get("agent_check_result");
//        if (StringUtils.isEmpty(result)) {
//            return "{}";
//        }
//        if (StringUtils.equals(result, "")) {
//            map.put("agent_check_result", "0");
//        }
//        return JSONObject.toJSONString(map);
//    }

    private String aviatorProcessRecord(AgentKnowledgeConfig agentKnowledgeConfig, String featureData) throws ContinousLearningException {
        String avitorScriptCode = agentKnowledgeConfig.getInputDataAviator();
        // 判断脚本代码是否存在，不存在直接返回原数据
        if (com.dianping.zebra.util.StringUtils.isBlank(avitorScriptCode)) {
            return featureData;
        }

        // 存在脚本代码，执行Aviator脚本处理
        try {
            // 将featureData转换成Map<String, Object>
            Map<String, Object> dataMap = JSONObject.parseObject(featureData, Map.class);

            Map<String, Object> req = new HashMap<>();
            req.put("dataMap", dataMap);

            Expression compile = AviatorEvaluator.compile(avitorScriptCode, true);
            // 执行脚本，传入数据map
            String result = (String) compile.execute(req);
            return result;
        } catch (Exception e) {
            log.error("执行Aviator脚本失败, script: {}, featureData: {}, error: {}", avitorScriptCode, featureData, e.getMessage(), e);
            // 脚本处理失败时抛出异常，停止后续流程
            throw new ContinousLearningException("Aviator脚本处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从批处理结果中提取失败样本的sampleId列表
     *
     * @param itemResults 批处理结果
     * @return 失败样本的sampleId列表
     */
    private List<Long> extractFailedSampleIds(BatchProcessResult<ExecuteEvaluationContext.ExecuteEvaluationContextItem, Pair<Integer, Integer>> itemResults) {
        List<Long> failedSampleIds = new ArrayList<>();

        if (itemResults.getFailureResults() != null && !itemResults.getFailureResults().isEmpty()) {
            for (BatchProcessResult.ItemProcessResult<ExecuteEvaluationContext.ExecuteEvaluationContextItem, Pair<Integer, Integer>> failureResult : itemResults.getFailureResults()) {
                ExecuteEvaluationContext.ExecuteEvaluationContextItem item = failureResult.getItem();
                if (item != null && item.getKnowledgeLabeledSample() != null) {
                    Long sampleId = item.getKnowledgeLabeledSample().getSampleId();
                    if (sampleId != null) {
                        failedSampleIds.add(sampleId);
                    }
                }
            }

            if (!failedSampleIds.isEmpty()) {
                log.warn("处理失败的样本ID列表: {}", failedSampleIds);
            }
        }

        return failedSampleIds;
    }


}
