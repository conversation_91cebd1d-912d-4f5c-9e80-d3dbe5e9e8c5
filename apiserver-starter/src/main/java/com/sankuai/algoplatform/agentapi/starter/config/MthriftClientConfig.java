package com.sankuai.algoplatform.agentapi.starter.config;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.xm.openplatform.api.service.open.OpenCardServiceI;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;


@Configuration
@Slf4j
public class MthriftClientConfig {

    @MdpThriftClient(remoteAppKey = "com.sankuai.dxenterprise.open.gateway", filterByServiceName = true, timeout = 3000)
    private XmAuthServiceI.Iface xmAuthService;
    @MdpThriftClient(remoteAppKey = "com.sankuai.dxenterprise.open.gateway", filterByServiceName = true, timeout = 3000)
    private OpenCardServiceI.Iface openCardService;


}
