package com.sankuai.algoplatform.agentapi.starter.controller;

import com.dianping.cat.util.StringUtils;
import com.sankuai.algoplatform.agentapi.starter.entity.SyncKnowledgeRequest;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import com.sankuai.algoplatform.agentapi.client.response.TKnowledgeRespone;
import com.sankuai.algoplatform.agentapi.starter.service.impl.FuXiKnowledgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/7/6
 */

@RestController
@RequestMapping("/learning")
@Slf4j
public class ContinousLearnningController {

    @Resource
    private FuXiKnowledgeService fuXiKnowledgeService;

    @PostMapping("/syncKnowledge")
    public TBaseReponse collectKnowledges(@RequestBody SyncKnowledgeRequest req) {
        String validationError = validateRequest(req);
        if (validationError != null) {
            return TKnowledgeRespone.paramsFail(validationError);
        }
        try {
            Map<String, String> knowledgeEvaluationByKnowledgeId = fuXiKnowledgeService.getKnowledgeEvaluationByKnowledgeId(req);
            return TKnowledgeRespone.success(knowledgeEvaluationByKnowledgeId);
        } catch (Exception e) {
            log.error("查询知识评测记录失败, req: {}, error: {}", req, e.getMessage(), e);
            return TKnowledgeRespone.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 校验请求参数
     */
    private String validateRequest(SyncKnowledgeRequest req) {
        if (req == null) {
            return "请求参数不能为空";
        }

        Boolean updateAll = req.getUpdateAll();
        String agentCode = req.getAgentCode();
        String partitionDate = req.getPartitionDate();
        List<String> knowledgeKeys = req.getKnowledgeKeys();

        // 如果updateAll为true，需要校验agentCode和partitionDate
        if (updateAll) {
            if (agentCode == null || agentCode.trim().isEmpty()) {
                return "全量更新时agentCode不能为空";
            }
            if (partitionDate == null || partitionDate.trim().isEmpty()) {
                return "全量更新时partitionDate不能为空";
            }
        } else {
            // 如果updateAll不为true，需要校验knowledgeKeys
            if (knowledgeKeys == null || knowledgeKeys.isEmpty()) {
                return "非全量更新时知识库key列表不能为空";
            }
            for (String key : knowledgeKeys) {
                if (StringUtils.isBlank(key)) {
                    return "知识库key列表中不能包含空值";
                }
            }
        }

        return null;
    }

}
