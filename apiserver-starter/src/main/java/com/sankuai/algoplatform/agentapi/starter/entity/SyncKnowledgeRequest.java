package com.sankuai.algoplatform.agentapi.starter.entity;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.List;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/7/6
 */

@Data
public class SyncKnowledgeRequest {

    @FieldDoc(description = "agentCode")
    private String agentCode;

    @FieldDoc(description = "知识库key列表")
    private List<String> knowledgeKeys;

    @FieldDoc(description = "是否更新全部知识到正式库")
    private Boolean updateAll = false;

    @FieldDoc(description = "分区日期")
    private String partitionDate;
}
