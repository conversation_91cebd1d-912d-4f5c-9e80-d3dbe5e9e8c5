package com.sankuai.algoplatform.agentapi.starter.job;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.algoplatform.agentapi.starter.service.ContinousLearningService;
import groovy.util.logging.Slf4j;

import javax.annotation.Resource;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */
@Slf4j
@CraneConfiguration
public class ContinuousLearningJob {

    @Resource
    private ContinousLearningService continousLearningService;

    @Crane("collectKnowledgePeriodically")
    public void collectKnowledgePeriodically() {
        continousLearningService.collectKnowledgePeriodically();
    }
    @Crane("monitorExperienceUpdate")
    public void monitorExperienceUpdate() {
        continousLearningService.monitorExperienceUpdate();
    }


}
