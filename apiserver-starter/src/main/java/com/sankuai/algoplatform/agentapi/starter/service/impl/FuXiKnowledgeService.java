package com.sankuai.algoplatform.agentapi.starter.service.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsTextVectorWriteRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.CommonResponse;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsTextVectorBuildResponse;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamQueryThriftService;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamWriteThriftService;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation;
import com.sankuai.algoplatform.agentapi.infrastructure.dao.KnowledgeEvaluationDao;
import com.sankuai.algoplatform.agentapi.starter.entity.SyncKnowledgeRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static com.sankuai.algoplatform.agentapi.infrastructure.constant.LionConfig.AGENT_KNOWLEDGE_MAP;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/26
 */

@Service
@Slf4j
public class FuXiKnowledgeService {


    @Resource
    private IDataStreamWriteThriftService dataStreamWriteThriftService;

    @Resource
    private IDataStreamQueryThriftService dataStreamQueryThriftService;

    @Resource
    private KnowledgeEvaluationDao knowledgeEvaluationDao;

    public DsTextVectorBuildResponse textVectorBuild(String originContent, Long dataStreamId) throws TException {
        if (StringUtils.isBlank(originContent) || dataStreamId == null) {
            return CommonResponse.Factory.createError(DsTextVectorBuildResponse.class, new IllegalArgumentException("originContent is blank"));
        }
        DsTextVectorWriteRequest request = new DsTextVectorWriteRequest();
        request.setOriginContent(originContent);
        request.setDataStreamId(dataStreamId);
        DsTextVectorBuildResponse dsTextVectorBuildResponse = dataStreamWriteThriftService.textVectorBuild(request);
        return dsTextVectorBuildResponse;
    }

    public DsTextVectorBuildResponse textVectorDelete(String originContent, Long dataStreamId) throws TException {
        if (StringUtils.isBlank(originContent) || dataStreamId == null) {
            return CommonResponse.Factory.createError(DsTextVectorBuildResponse.class, new IllegalArgumentException("originContent is blank"));
        }
        DsTextVectorWriteRequest request = new DsTextVectorWriteRequest();
        request.setOriginContent(originContent);
        request.setDataStreamId(dataStreamId);
        DsTextVectorBuildResponse dsTextVectorBuildResponse = dataStreamWriteThriftService.textVectorDelete(request);
        return dsTextVectorBuildResponse;
    }

    public Map<String, String> getKnowledgeEvaluationByKnowledgeId(SyncKnowledgeRequest req) throws Exception {
        String agentCode = req.getAgentCode();
        List<String> knowledgeKeys = req.getKnowledgeKeys();
        String partitionDate = req.getPartitionDate();
        Boolean updateAll = req.getUpdateAll();

        log.info("开始查询知识评测记录, agentCode: {}, updateAll: {}, partitionDate: {}, knowledgeKeys: {}",
                agentCode, updateAll, partitionDate, knowledgeKeys);
        List<KnowledgeEvaluation> queryResult = Collections.emptyList();
        try {
            if (updateAll && !StringUtils.isBlank(agentCode) && !StringUtils.isBlank(partitionDate)) {
                queryResult = knowledgeEvaluationDao.selectByAgentCodeAndPartitionDateAndStatus(agentCode, partitionDate, 2, 1);
                log.info("全量更新查询完成, agentCode: {}, partitionDate: {}, 查询到{}条记录", agentCode, partitionDate, queryResult.size());
            } else {
                log.info("执行指定知识key查询, knowledgeKeys: {}", knowledgeKeys);
                if (CollectionUtils.isEmpty(knowledgeKeys)) {
                    throw new IllegalArgumentException("knowledgeKeys为空");
                }
                queryResult = knowledgeEvaluationDao.selectByKnowledgeKeysAndStatus(knowledgeKeys, 2, 1);
            }
            Map<String, String> result = uploadKnowledge(queryResult);
            log.info("上传知识完成, 查询到{}条记录, 上传成功{}条, 上传失败{}条", result.get("total"), result.get("success"), result.get("fail"));
            return result;
        } catch (Exception e) {
            log.error("查询知识评测记录失败, agentCode: {}, updateAll: {}, partitionDate: {}, knowledgeKeys: {}, error: {}", agentCode, updateAll, partitionDate, knowledgeKeys, e.getMessage(), e);
            throw e;
        }
    }

    private Map<String, String> uploadKnowledge(List<KnowledgeEvaluation> knowledgeEvaluation) throws TException {
        Map<String, String> result = new HashMap<>();
        Set<String> successList = new HashSet<>();
        Set<String> failList = new HashSet<>();

        // 基于 knowledge_key 去重，保留第一个出现的记录
        Map<String, KnowledgeEvaluation> distinctMap = new LinkedHashMap<>();
        for (KnowledgeEvaluation evaluation : knowledgeEvaluation) {
            if (!distinctMap.containsKey(evaluation.getKnowledgeKey())) {
                distinctMap.put(evaluation.getKnowledgeKey(), evaluation);
            }
        }

        List<KnowledgeEvaluation> distinctEvaluations = new ArrayList<>(distinctMap.values());
        log.info("去重前记录数: {}, 去重后记录数: {}", knowledgeEvaluation.size(), distinctEvaluations.size());

        for (KnowledgeEvaluation evaluation : distinctEvaluations) {
            DsTextVectorBuildResponse dsTextVectorBuildResponse = textVectorBuild(evaluation.getKnowledgeData(), AGENT_KNOWLEDGE_MAP.get(evaluation.getAgentCode()).getKnowBaseId());
            if (dsTextVectorBuildResponse.getSuccess()) {
                successList.add(evaluation.getKnowledgeKey());
            } else {
                failList.add(evaluation.getKnowledgeKey());
            }
        }
        result.put("total", String.valueOf(knowledgeEvaluation.size()));
        result.put("success", String.valueOf(successList.size()));
        result.put("fail", String.valueOf(failList.size()));
        return result;
    }

}
