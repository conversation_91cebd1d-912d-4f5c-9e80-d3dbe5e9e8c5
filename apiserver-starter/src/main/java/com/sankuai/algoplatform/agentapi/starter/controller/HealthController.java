package com.sankuai.algoplatform.agentapi.starter.controller;

import com.sankuai.algoplatform.agentapi.infrastructure.util.HttpConnectionMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供系统健康状态和HTTP连接池监控信息
 * 
 * <AUTHOR>
 * @date 2025年07月10日
 */
@RestController
@RequestMapping("/health")
@Slf4j
public class HealthController {

    @Autowired
    private HttpConnectionMonitor connectionMonitor;

    /**
     * 基础健康检查
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(health);
    }

    /**
     * HTTP连接池健康检查
     */
    @GetMapping("/http-pool")
    public ResponseEntity<Map<String, Object>> httpPoolHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            boolean isHealthy = connectionMonitor.isConnectionPoolHealthy();
            String stats = connectionMonitor.getConnectionPoolStats();
            
            health.put("status", isHealthy ? "UP" : "DOWN");
            health.put("details", stats);
            health.put("timestamp", System.currentTimeMillis());
            
            if (isHealthy) {
                return ResponseEntity.ok(health);
            } else {
                return ResponseEntity.status(503).body(health);
            }
            
        } catch (Exception e) {
            log.error("HTTP连接池健康检查失败", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(503).body(health);
        }
    }

    /**
     * 详细的系统健康检查
     */
    @GetMapping("/detailed")
    public ResponseEntity<Map<String, Object>> detailedHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 基础信息
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());
            
            // JVM信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> jvm = new HashMap<>();
            jvm.put("maxMemory", runtime.maxMemory());
            jvm.put("totalMemory", runtime.totalMemory());
            jvm.put("freeMemory", runtime.freeMemory());
            jvm.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            jvm.put("availableProcessors", runtime.availableProcessors());
            health.put("jvm", jvm);
            
            // HTTP连接池信息
            Map<String, Object> httpPool = new HashMap<>();
            httpPool.put("healthy", connectionMonitor.isConnectionPoolHealthy());
            httpPool.put("stats", connectionMonitor.getConnectionPoolStats());
            health.put("httpConnectionPool", httpPool);
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            log.error("详细健康检查失败", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(503).body(health);
        }
    }
}
