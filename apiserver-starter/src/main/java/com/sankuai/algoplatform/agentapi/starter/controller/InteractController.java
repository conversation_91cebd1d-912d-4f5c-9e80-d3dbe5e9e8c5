package com.sankuai.algoplatform.agentapi.starter.controller;

import com.sankuai.algoplatform.agentapi.domain.service.impl.SseProxyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * SSE连接控制器
 * 用于建立服务器发送事件(Server-Sent Events)连接
 * 作为入口层，将请求代理到SseProxyService进行处理
 */
@RestController
@RequestMapping("/agent")
@Slf4j
public class InteractController {

    private final SseProxyService sseProxyService;

    @Autowired
    public InteractController(SseProxyService sseProxyService) {
        this.sseProxyService = sseProxyService;
    }

    /**
     * 建立SSE连接
     *
     * @param session_id 会话ID，用于标识连接（可选）
     * @param mis_id 美团内部员工ID（必填）
     * @return SseEmitter 实例
     */
    @GetMapping(value = "/sse/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(@RequestParam String mis_id, @RequestParam(required = false) String session_id, @RequestParam String agent_code) {
        log.info("SSE连接请求: sessionId={}, misId={}, agentCode={}", session_id, mis_id, agent_code);
        try {
            SseEmitter emitter = sseProxyService.connect(session_id, mis_id, agent_code);
            log.info("SSE连接建立成功: sessionId={}, misId={}", session_id, mis_id);
            return emitter;
        } catch (Exception e) {
            log.error("SSE连接建立失败: sessionId={}, misId={}, agentCode={}", session_id, mis_id, agent_code, e);
            throw e;
        }
    }

    /**
     * 关闭指定客户端的连接
     *
     * @param clientId 客户端ID
     * @return 关闭结果
     */
    @GetMapping("/close/{clientId}")
    public String closeConnection(@PathVariable String clientId) {
        log.info("关闭SSE连接请求: sessionId={}", clientId);
        try {
            String result = sseProxyService.closeConnection(clientId);
            log.info("关闭SSE连接成功: sessionId={}, result={}", clientId, result);
            return result;
        } catch (Exception e) {
            log.error("关闭SSE连接失败: sessionId={}", clientId, e);
            throw e;
        }
    }

    /**
     * 获取当前活跃连接数
     *
     * @return 活跃连接数
     */
    @GetMapping("/count")
    public String getActiveConnectionsCount() {
        try {
            int count = sseProxyService.getActiveConnectionsCount();
            log.info("查询活跃连接数: count={}", count);
            return "Active connections: " + count;
        } catch (Exception e) {
            log.error("查询活跃连接数失败", e);
            throw e;
        }
    }
}