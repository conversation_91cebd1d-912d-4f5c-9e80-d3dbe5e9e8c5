package com.sankuai.algoplatform.agentapi.starter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.google.common.collect.ImmutableMap;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsTextVectorBuildResponse;
import com.sankuai.algoplatform.agentapi.client.request.TKnowledgeCollectRequest;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentLearningService;
import com.sankuai.algoplatform.agentapi.infrastructure.dal.rds.po.KnowledgeEvaluation;
import com.sankuai.algoplatform.agentapi.infrastructure.config.AgentKnowledgeConfig;
import com.sankuai.algoplatform.agentapi.infrastructure.dao.KnowledgeEvaluationDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.util.DateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

import static com.sankuai.algoplatform.agentapi.infrastructure.constant.LionConfig.AGENT_KNOWLEDGE_MAP;

/**
 * @program: algoplat-agentapi
 * <AUTHOR>
 * @Date 2025/6/24
 */

@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
@Service
@Slf4j
public class TAgentLearningServiceImpl implements TAgentLearningService {

    @Resource
    private FuXiKnowledgeService fuXiKnowledgeService;

    @Resource
    private KnowledgeEvaluationDao knowledgeEvaluationDao;

    @Override
    public TBaseReponse collectKnowledges(TKnowledgeCollectRequest req){
        String collectId = String.valueOf(req.getCollectId());
        String agentCode = req.getAgentCode();
        Long knowledgeKey = req.getKnowledgeKey();
        String knowledgeKeyStr = String.valueOf(knowledgeKey);
        Cat.logMetricForCount("knowledgeCollect.count", 1, ImmutableMap.of("agentCode", agentCode));
        try {
            log.info("=== 开始处理知识收集请求 === collectId: {}, agentCode: {}, knowledgeKey: {}, knowledgeData: {}", collectId, agentCode, knowledgeKeyStr, req.getKnowledgeData());
            String checkParams = checkParam(req);
            if (!StringUtils.isBlank(checkParams)) {
                log.error("参数校验失败, collectId: {}, error: {}", collectId, checkParams);
                Cat.logMetricForCount("knowledgeCollect.paramsVaildFailed", 1, ImmutableMap.of("agentCode", agentCode));
                return TBaseReponse.paramsFail(checkParams);
            }

            // 保存经验
            log.info("开始保存知识评估记录, collectId: {}, knowledgeKey: {}", collectId, knowledgeKeyStr);
            KnowledgeEvaluation knowledgeEvaluation = new KnowledgeEvaluation();
            knowledgeEvaluation.setKnowledgeData(req.getKnowledgeData());
            knowledgeEvaluation.setKnowledgeKey(knowledgeKeyStr);
            knowledgeEvaluation.setPartitionDate(DateUtil.formatDate(new Date(), "yyyy-MM-dd"));
            knowledgeEvaluation.setEvalStatus(0);
            knowledgeEvaluation.setEvalResult(0);
            knowledgeEvaluation.setEvalReport("{}");
            knowledgeEvaluation.setAgentCode(req.getAgentCode());
            knowledgeEvaluation.setCollectId(req.getCollectId());
            knowledgeEvaluationDao.insertKnowledgeEvaluation(knowledgeEvaluation);
            log.info("知识评估记录插入成功，knowledgeKey: {}, agentCode: {}, collectId: {}", knowledgeKeyStr, req.getAgentCode(), req.getCollectId());

            // 上传到测评知识库
            AgentKnowledgeConfig agentKnowledgeConfig = AGENT_KNOWLEDGE_MAP.get(req.getAgentCode());
            long startTime = System.currentTimeMillis();
            DsTextVectorBuildResponse dsTextVectorBuildResponse = fuXiKnowledgeService.textVectorBuild(req.getKnowledgeData(), agentKnowledgeConfig.getAssKnowBaseId());
            long endTime = System.currentTimeMillis();
            if (!dsTextVectorBuildResponse.isSuccess()) {
                log.error("上传到知识库失败, collectId: {}, knowledgeKey: {}, agentCode: {}, costTime: {}ms, error: {}", collectId, knowledgeKeyStr, agentCode, (endTime - startTime), dsTextVectorBuildResponse.getMsg());
                Cat.logMetricForCount("knowledgeCollect.fuxiError", 1, ImmutableMap.of("agentCode", agentCode));
                return TBaseReponse.fail("上传到知识库失败，伏羲接口异常：" + dsTextVectorBuildResponse.getMsg());
            }
            log.info("上传到知识库成功！collectId: {}, knowledgeKey: {}, costTime: {}ms, response: {}",
                    collectId, knowledgeKeyStr, (endTime - startTime), JSONObject.toJSONString(dsTextVectorBuildResponse));
            Cat.logMetricForCount("knowledgeCollect.success", 1, ImmutableMap.of("agentCode", agentCode));
            return TBaseReponse.success();
        } catch (Exception e) {
            Cat.logMetricForCount("knowledgeCollect.error", 1, ImmutableMap.of("agentCode", agentCode));
            log.error("collectKnowledges error, req:{}", req, e);
            return TBaseReponse.fail("系统异常");
        }
    }

    private String checkParam(TKnowledgeCollectRequest req) {
        if (req == null) {
            return "请求为空";
        }
        String agentCode = req.getAgentCode();
        String knowledgeData = req.getKnowledgeData();
        String knowledgeKey = String.valueOf(req.getKnowledgeKey());
        if (StringUtils.isBlank(agentCode)  || StringUtils.isBlank(knowledgeData) || StringUtils.isBlank(knowledgeKey)) {
            return "参数不合法";
        }
        return null;

    }
}
