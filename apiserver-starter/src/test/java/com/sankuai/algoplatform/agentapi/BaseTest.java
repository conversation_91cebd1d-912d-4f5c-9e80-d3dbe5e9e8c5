package com.sankuai.algoplatform.agentapi;

import com.meituan.mtrace.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertTrue;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationLoader.class)
@Slf4j
public class BaseTest {

    @Test
    public void ready() {
        assertTrue(Boolean.TRUE);
    }

    @Before
    public void before() {
        Tracer.serverRecv();
        log.info("traceId: {}", Tracer.id());
    }

    @After
    public void after() {
        Tracer.serverSend();
    }
}
