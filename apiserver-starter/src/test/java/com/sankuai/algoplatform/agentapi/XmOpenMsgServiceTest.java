package com.sankuai.algoplatform.agentapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.algoplatform.agentapi.client.request.SendGroupCardRequest;
import com.sankuai.algoplatform.agentapi.client.request.SendMisCardRequest;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import com.sankuai.algoplatform.agentapi.client.service.TOpenCardService;
import com.sankuai.algoplatform.agentapi.domain.service.impl.TOpenCardCallbackServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
public class XmOpenMsgServiceTest extends BaseTest {
    @Autowired
    private TOpenCardService tOpenCardService;
    @Autowired
    private TOpenCardCallbackServiceImpl tOpenCardCallbackService;

    @Test
    public void all() {
        try {
            sendMisCard0();
        } catch (Exception e) {
            log.error("sendGroupCard error", e);
        }

        try {
            sendGroupCard();
        } catch (Exception e) {
            log.error("sendGroupCard error", e);
        }

        try {
            sendMisCard();
        } catch (Exception e) {
            log.error("sendMisCard error", e);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("confirmCallback", "如何接入新业务或新竞对?");
        jsonObject.put("input", "\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗");
        jsonObject.put("txt", "\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗[zhangkexin25]已启动匹配Agent任务，点击链接接管任务：https://km.sankuai.com/collabpage/2080071229，任务完成后输入匹配效果报告链接，并点击【确认】");
        jsonObject.put("templateId", "22844");
        jsonObject.put("buttonEnable", "false");
        tOpenCardCallbackService.updateSharedCard("1752462296930", 22844L, jsonObject);
    }

    @Test
    public void sendMisCard0() {
        Exception exception = null;
        try {
            Map<String, String> variableValue = new HashMap<>();
            variableValue.put("txt", "\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗[zhangkexin25]已启动匹配Agent任务，点击链接接管任务：https://km.sankuai.com/collabpage/2080071229，任务完成后输入匹配效果报告链接，并点击【确认】");
            variableValue.put("confirmCallback", "如何接入新业务或新竞对?");
            TBaseReponse tBaseReponse = tOpenCardService.sendMisCardWithoutSessionId(22844L, Arrays.asList("zhangkexin25", "caotianyuan", "huangjiandong04"), "【单聊无sessionId】[zhangkexin25]已启动匹配Agent任务", variableValue);
            log.info("tBaseReponse:{}", JSON.toJSONString(tBaseReponse));
        } catch (Exception e) {
            log.error("发送卡片消息异常", e);
            exception = e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void sendGroupCard() {
        Exception exception = null;
        try {
            Map<String, String> variableValue = new HashMap<>();
            variableValue.put("txt", "\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗[zhangkexin25]已启动匹配Agent任务，点击链接接管任务：https://km.sankuai.com/collabpage/2080071229，任务完成后输入匹配效果报告链接，并点击【确认】");
            variableValue.put("confirmCallback", "如何接入新业务或新竞对?");
            // 构造请求对象
            SendGroupCardRequest request = new SendGroupCardRequest();
            request.setTemplateId(22844L);
            request.setGid(64014512296L);
            request.setAbstractText("【群聊】[zhangkexin25]已启动匹配Agent任务");
            request.setSessionId("9bdf799e226548e99e04621886b1d042");
            request.setVariableValue(variableValue);

            TBaseReponse tBaseReponse = tOpenCardService.sendGroupCard(request);
            log.info("tBaseReponse:{}", JSON.toJSONString(tBaseReponse));
        } catch (Exception e) {
            log.error("发送卡片消息异常", e);
            exception = e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void sendMisCard() {
        Exception exception = null;
        try {
            Map<String, String> variableValue = new HashMap<>();
            variableValue.put("txt", "【单聊】\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗已启动匹配Agent任务，点击链接接管任务：https://km.sankuai.com/collabpage/2080071229，任务完成后输入匹配效果报告链接，并点击【确认】");
            variableValue.put("confirmCallback", "如何接入新业务或新竞对?");
//            variableValue.put("sendMisId", "zhangkexin25");
            // 构造请求对象
            SendMisCardRequest request = new SendMisCardRequest();
            request.setTemplateId(22844L);
            request.setMisList(Arrays.asList("zhangkexin25", "caotianyuan", "huangjiandong04"));
            request.setAbstractText("\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0\uD83C\uDFE0🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗🚗[zhangkexin25]已启动匹配Agent任务");
            request.setSessionId("9bdf799e226548e99e04621886b1d042");
            request.setVariableValue(variableValue);

            TBaseReponse tBaseReponse = tOpenCardService.sendMisCard(request);
            request.setTemplateId(22844L);
            TBaseReponse tBaseReponse2 = tOpenCardService.sendMisCard(request);
            request.setTemplateId(22844L);
            TBaseReponse tBaseReponse3 = tOpenCardService.sendMisCard(request);
            log.info("tBaseReponse:{}", JSON.toJSONString(tBaseReponse));
        } catch (Exception e) {
            log.error("发送卡片消息异常", e);
            exception = e;
        }
        Assert.assertNull(exception);
    }
}
